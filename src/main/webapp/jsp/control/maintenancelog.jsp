<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.PageDetails"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><fmt:message key="MaintenanceLog.title.window"/></title>
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<link rel="stylesheet" type="text/css" href="style/displaytag.css" >

<SCRIPT language="JAVASCRIPT">
var dateFlag=true;
mandatoryFieldsArray = ["mainLogFromDate","mainLogToDate"] ;
var totalCount = '${totalCount}';
var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var maxPage = "${requestScope.maxPage}";
var currPage = '${requestScope.currentPage}';
var lastRefTime = "${requestScope.lastRefTime}";
var dateFormat = '${sessionScope.CDM.dateFormat}';
var filterValues=new Array();
var currentFilterValues = currentFilter.split("|");
var sortingValues = currentSort.split("|");
var sortedValues = new Array();
sortedValues[0] = sortingValues[0];
sortedValues[1] = sortingValues[1];
var dateSelected = false;


function buildTableNameURL(methodName, selectedLogDate, selectedUserId, selectedIpAddress, selectedTableName,selectedReference, selectedAction) {
  var param = 'maintenancelog.do?method=' + methodName;

  param += '&selectedLogDate=' + selectedLogDate;
  param += '&selectedUserId=' + selectedUserId;
  param += '&selectedIpAddress=' + selectedIpAddress;
  param += '&selectedTableName=' + selectedTableName;
  param += '&selectedReference=' + selectedReference;
  param += '&selectedAction=' + selectedAction;

  window.openWindow(param, '', 'left=50,top=190,width=1080,height=610,toolbar=0,resizable=yes,scrollbars=yes', 'true');
}


</SCRIPT>

<script language="JAVASCRIPT">
var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
      var cal = new CalendarPopup("caldiv",true);

      cal.offsetX = 22;
      cal.offsetY = 0;
	  var cal2 = new CalendarPopup("caldiv",true);

      cal2.offsetX = 22;
      cal2.offsetY = 0;
  </script>


</SCRIPT>
</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus(); setFocus(document.forms[0]);" onunload="call();">
<form action="maintenancelog.do" method="post" method="post" onsubmit="return validate(this);">
  <input name="method" type="hidden" value="display">
  <input name="selectedTableName" type="hidden" value="display">
  <input name="selectedColumnName" type="hidden" value="display">
  <input name="selectedLogDate" type="hidden" value="">
  <input name="selectedDate" type="hidden" value="">
  <input name="selectedTime" type="hidden" value="">
  <input name="selectedMainSeq" type="hidden" value="">
  <input name="selectedUserId" type="hidden" value="">
  <input name="selectedIpAddress" type="hidden" value="">
  <input name="selectedReference" type="hidden" value="">
  <input name="selectedAction" type="hidden" value="">
  <input name="totalCount" type="hidden" value="">

  <input name="selectedFilter" type="hidden" value='${selectedFilter}'>
  <input name="selectedSort" type="hidden" value='${selectedSort}'>

  <input name="preMaintenanceFromDateAsString" type="hidden" value="">
  <input name="preMaintenanceToDateAsString" type="hidden" value="">

  <input name="goToPageNo" type="hidden" value="">
  <input name="fromDate" type="hidden" value="">
  <input name="toDate" type="hidden" value="">
  <input name="maxPages" type="hidden" value="">
  <input name="currentPage" type="hidden" value="">

</form>
</body>
<link rel="stylesheet"	href="angularSources/styles.css?version=<%=SwtUtil.appVersion%>">
<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "MaintenanceLog";
</script>

<%@ include file="/angularJSUtils.jsp"%>
<%@ include file="/angularscripts.jsp"%>

</html>