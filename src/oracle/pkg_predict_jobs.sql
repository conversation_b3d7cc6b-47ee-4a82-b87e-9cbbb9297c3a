CREATE OR REPLACE PACKAGE PKG_PREDICT_JOBS AS
/*
Modification History
****************************************************************************************************
Release  Who          Date     Description
-------- ------------ -------- ---------------------------------------------------------------------
1069     <USER>        <GROUP>/09/22  Mantis 3487: Sweeping: Predicted balances used by sweeping should be consistent with those in monitors
1068     Rchatbouri   10/02/22 Mantis 5702: Loro/Predicted Balance Contribution: Allow Account balance contribution to deviate from default during a defined period.
1068     Rchatbouri   28/01/22 Mantis 5583: Allow cross-entity sweeps to be created
1067     RChatbouri   18/11/21 Scheduled job fails with unique constraint violation
1064.2   RChatbouri   09/12/20 Mantis 5029: ILM Monitor: Layout amendment to include multiple Entity/Ccy tabs
1063.8   RChatbouri   24/12/19 Mantis 4845: Account Breakdown Monitor: Not able to show balances later than the range calculated by the populate job
1061     SChebka      10/04/18 Mantis 4085: Apply correction to logic in PKG_PREDICT_JOBS regarding open movements
1060     SChebka      10/05/17 Mantis 3435: Entity/Currency/Account monitor balances for T+n should include open unexpected and open unsettled movements
1057     STL-LDN      10/04/15 Mantis 2633: No. of days before/after now configurable in P_MISC_PARAMS
         Rchatbouri   09/01/15 Mantis 2549: Predicted balance calculation - open movement match status condition correction
1056.3   Rchatbouri   08/01/15 Mantis 2228: Some calls to functions being passed incorrect parameters
1056     STL-LDN      10/09/14 Mantis 2479: Replace ampersand in Oracle scripts
1055.2   JMatoussi    30/05/14 Mantis 2438: Position level totals not updating due to workflow job not maintaining P_WORKFLOW_CHART_TEMP
1055     Rchatbouri   08/08/13 Mantis 1443: Remove objects no longer used
1054     Rchatbouri   17/06/13 Mantis 2276: Tune and add hints to tOpenMov queries in PKG_PREDICT_JOBS
         Rchatbouri   19/04/13 Mantis 2232: Amend method for flagging accounts for balance processing and currencies for matching
                                 Remove spUpdateAccountProcessFlag procedure and calls to it and
                                 replace with PK_APPLICATION.SET_ACCOUNT_PROCESS_FLAG
1055     Rchatbouri   15/04/13 Mantis 1443: Enhanced Alerting: Remove the count of 'Reconciled' from workflow monitor
         Haykel       26/12/12 Mantis 1443: Enhanced Alerting: Remove Existing Alerting Code
         Rchatbouri   13/11/12  Mantis 2098: Workflow Monitor: Slow to refresh when tab selected is other than today
                                 - create SP_UPDATE_WORKFLOW_JOB_SEQ: fill the p_workflow_chart_temp
                                   table for dates other than system date
                                 - keep SP_UPDATE_WORKFLOW_JOB only for the system date
         KaisBS       05/11/12 Mantis 2016: Create fn_get_days_prior_to_today and fn_get_days_ahead_to_today
         Venky        18/10/12 Mantis 1768: Workflow monitor: Unsettled / unexpected yesterday counts need not include movements input today
                                 Changes made to call the 'global_var.fn_get_offset_date_ent' function for input_date threshold calculation
                      05/10/12 Included the input date threshold checking in 'cr_usm' and 'cr_ouem' cursors
                      21/09/12 1. Used Entity offset date while getting the previous business date
                                  for 'unsettled' and 'unexpected' movement counts
                               2. Intialisation/Update done for the unsettled, unexpected and
                                  back valued movement count before the count calculation process
                               3. Intialisation/Update done for the sweep exception count before the sweep exception process
         Venky        14/09/12 Mantis 1937: INTERNAL: PK_MONITORS.sp_upd_workflow_monitor_temp - CR_SWEEP cursor requires extra join
                                 Added host_id in subquery of first outer join of the cursor to help performance
1053.7   STL-LDN      27/07/12 Mantis 2018: Populate and update record jobs to use entity time offset
1054     Chandrasekar 02/07/12 Mantis 1933: Account Monitor: Screen hangs with "Function returned without value" if closed accounts
                                  (Defect: 1054_STL_063, 64, 65 and 66)
                                 1. Added filter in sp_populate_data and sp_add_additional_dates procedures
                                    to get the closed account based on monitor and link_account_id
                                 2. Removed s_currency_group table unsettled and unexpected query
         Swaminathan  25/06/12 Mantis 1119: PK_MONITORS package enhancements (Defect: 1054_STL_042)
                                 1. Implemented FORALL SAVE EXCEPTION in SP_POPULATE_DATA and removed s_currency_group
                                    table in SP_UPD_WORKFLOW_MONITOR_TEMP procedure cr_ouem and cr_usmm cursors
1053.6   Chandrasekar 14/06/12 Mantis 1966: Matching does not stop
                                 Added spUpdateAccountProcessFlag procedure with autonomous transaction
1054     Chandrasekar 25/05/12 Mantis 1933: Account Monitor: Screen hangs with "Function returned without value" if closed accounts
                                 1. Added filter for closed account in SP_POPULATE_DATA function
                                    and insert data for account_status_flag column in p_monitors_temp
                                 2. Added filter for closed account in SP_ADD_ADDITIONAL_DATES function
                                    and insert data for account_status_flag column in p_monitors_temp
1053.5   Chandrasekar 16/05/12 Mantis 1932: Close opened cursor in PK_MONITORS.SP_UPDATE_GROUP_DATA package
1053.4   Raj          15/05/12 Mantis 1927: Workflow monitor performance improvement
                                 CURSOR cr_usm [Unsettled movements count] modified in SP_UPD_WORKFLOW_MONITOR_TEMP
1053.3   Swaminathan  23/04/12 Mantis 1904: Deadlock issue on P_GROUP_MONITOR
                                 Introduced FOR ALL and SAVE EXCEPTION in procedure SP_UPDATE_GROUP_DATA
1054     Swaminathan  25/04/12 Mantis 1663: PK_MONITORS: Change date range calculated in populate data and update records jobs
                                 Reduced 'n_days_prior_to_today' value from 5 days to 3 days
         Swaminathan  30/03/12 Mantis 1728: Performance problem with job that updates priority records
                                 WITH clause introduced for OPEN movements in following functions
                                 1. FN_GET_PREDICT_BALANCE      2. FN_GET_PREDICT_BALANCE_LORO   3. FN_GET_OPEN_UNEXPECTED_BALANCE
                                 4. FN_GET_UNSETTLED_BALANCE    5. FN_GET_UNEXPECTED_BALANCE
         Swaminathan  22/03/12 Mantis 1119: PK_MONITORS package enhancements
                                 In FN_GET_LORO_SOD_FUTURE_ADJ, address defect 1054_SEL_001
         Balaji.R     24/02/12 Mantis 1721: Populate group monitor background process: Unique constraint violation
                                 Improve performance in sp_populate_group_data
         Swaminathan  05/12/11 Mantis 1119: PK_MONITORS package enhancements
                                 a) Jobs related procedures/functions are moved from PK_MONITORS to this package
                                 b) Removed hints IDX_MOVE_HST_CUR_AC_VD_PRED; IDX_P_MOVE_HST_EN_CR_VD_BC; IDX_P_MOVEMENT_OPEN
****************************************************************************************************
*/

   CONST_WORKFLOW_DAYS   CONSTANT   PLS_INTEGER  := 6;
   CONST_SYSTEM          CONSTANT   VARCHAR2(30) := 'SYSTEM';
   CONST_DBSERVER        CONSTANT   VARCHAR2(30) := 'DBSERVER';

   TYPE filters IS RECORD(
      cash_filter       s_entity.cash_filter%TYPE,
      securities_filter s_entity.securities_filter%TYPE
   );

   TYPE ex_matrix1 IS TABLE OF filters    INDEX BY VARCHAR2(12);
   TYPE ex_matrix2 IS TABLE OF ex_matrix1 INDEX BY VARCHAR2(12);
   TYPE ex_matrix  IS TABLE OF ex_matrix2 INDEX BY VARCHAR2(12);

   TYPE t_host_id IS TABLE OF p_account.host_id%TYPE
      INDEX BY BINARY_INTEGER;

   TYPE t_entity_id IS TABLE OF p_account.entity_id%TYPE
      INDEX BY BINARY_INTEGER;

   TYPE t_account_id IS TABLE OF p_account.account_id%TYPE
      INDEX BY BINARY_INTEGER;

   v_account_id_process         t_account_id;
   v_host_id_process            t_host_id;
   v_entity_id_process          t_entity_id;

   PROCEDURE sp_populate_data;

   PROCEDURE sp_add_additional_dates(
      p_host_id        IN  VARCHAR2,
      p_entity_id      IN  VARCHAR2,
      p_currency_code  IN  VARCHAR2,
      p_account_id     IN  VARCHAR2,
      p_value_date     IN  DATE,
      p_current_date   IN  DATE
   );

   PROCEDURE sp_update_records_priority;

   PROCEDURE sp_update_records;

   PROCEDURE sp_populate_sweep_data;

   PROCEDURE sp_update_sweep_records;

   PROCEDURE sp_populate_group_data;

   PROCEDURE sp_populate_additional_gr_data(
      p_host_id       IN  VARCHAR2,
      p_entity_id     IN  VARCHAR2,
      p_currency_code IN  VARCHAR2,
      p_balance_date  IN  DATE
   );

   PROCEDURE sp_update_group_data;

   PROCEDURE sp_update_workflow_job;

   FUNCTION fn_get_int_sod_future_adj(
      p_host_id                   IN  VARCHAR2,
      p_entity_id                 IN  VARCHAR2,
      p_currency_code             IN  VARCHAR2,
      p_account_id                IN  VARCHAR2,
      p_account_type              IN  VARCHAR2,
      p_bv_adjust_basis_forecast      VARCHAR2,
      p_future_balance_method         VARCHAR2,
      p_value_date                IN  DATE,
      p_current_date              IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_ext_sod_future_adj(
      p_host_id                   IN  VARCHAR2,
      p_entity_id                 IN  VARCHAR2,
      p_currency_code             IN  VARCHAR2,
      p_account_id                IN  VARCHAR2,
      p_account_type              IN  VARCHAR2,
      p_bv_adjust_basis_external  IN  VARCHAR2,
      p_future_balance_method         VARCHAR2,
      p_value_date                IN  DATE,
      p_current_date              IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_loro_sod_future_adj(
      p_host_id                  IN  VARCHAR2,
      p_entity_id                IN  VARCHAR2,
      p_currency_code            IN  VARCHAR2,
      p_account_id               IN  VARCHAR2,
      p_account_type             IN  VARCHAR2,
      p_account_class            IN  VARCHAR2,
      p_this_entity_incl_bal_flag  IN  VARCHAR2,
      p_bv_adjust_basis_forecast IN  VARCHAR2,
      p_future_balance_method        VARCHAR2,
      p_value_date               IN  DATE,
      p_current_date             IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_start_balance(
      p_host_id                  IN  VARCHAR2,
      p_entity_id                IN  VARCHAR2,
      p_currency_code            IN  VARCHAR2,
      p_account_id               IN  VARCHAR2,
      p_account_type             IN  VARCHAR2,
      p_bv_adjust_basis_forecast IN  VARCHAR2,
      p_value_date               IN  DATE,
      p_current_date             IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_start_bal_external(
      p_host_id                   IN  VARCHAR2,
      p_entity_id                 IN  VARCHAR2,
      p_currency_code             IN  VARCHAR2,
      p_account_id                IN  VARCHAR2,
      p_account_type              IN  VARCHAR2,
      p_bv_adjust_basis_external  IN  VARCHAR2,
      p_value_date                IN  DATE,
      p_current_date              IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_start_bal_loro(
      p_host_id                    IN  VARCHAR2,
      p_entity_id                  IN  VARCHAR2,
      p_currency_code              IN  VARCHAR2,
      p_account_id                 IN  VARCHAR2,
      p_account_type               IN  VARCHAR2,
      p_account_class              IN  VARCHAR2,
      p_this_entity_incl_bal_flag  IN  VARCHAR2,
      p_bv_adjust_basis_forecast   IN  VARCHAR2,
      p_value_date                 IN  DATE,
      p_current_date               IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_predict_balance(
      p_host_id                IN  VARCHAR2,
      p_entity_id              IN  VARCHAR2,
      p_currency_code          IN  VARCHAR2,
      p_account_id             IN  VARCHAR2,
      p_start_balance_option   IN  VARCHAR2,
      p_value_date             IN  DATE,
      p_current_date           IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_predict_balance_ext(
      p_host_id         IN  VARCHAR2,
      p_entity_id       IN  VARCHAR2,
      p_currency_code   IN  VARCHAR2,
      p_account_id      IN  VARCHAR2,
      p_value_date      IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_predict_balance_loro(
      p_host_id              IN  VARCHAR2,
      p_entity_id            IN  VARCHAR2,
      p_currency_code        IN  VARCHAR2,
      p_account_id           IN  VARCHAR2,
      p_start_balance_option IN  VARCHAR2,
      p_account_class        IN  VARCHAR2,
      p_this_entity_incl_bal_flag  IN  VARCHAR2,
      p_value_date           IN  DATE,
      p_current_date         IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_unexpected_balance(
      p_host_id          IN  VARCHAR2,
      p_entity_id        IN  VARCHAR2,
      p_currency_code    IN  VARCHAR2,
      p_account_id       IN  VARCHAR2,
      p_threshold_flag   IN  VARCHAR2 DEFAULT 'Y',
      p_value_date       IN  DATE,
      p_current_date     IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_open_unexpected_balance(
      p_host_id        IN  VARCHAR2,
      p_entity_id      IN  VARCHAR2,
      p_currency_code  IN  VARCHAR2,
      p_account_id     IN  VARCHAR2,
      p_account_class  IN  VARCHAR2,
      p_value_date     IN  DATE,
      p_current_date   IN  DATE
   )
   RETURN NUMBER;

   FUNCTION fn_get_unsettled_balance(
      p_host_id        IN  VARCHAR2,
      p_entity_id      IN  VARCHAR2,
      p_currency_code  IN  VARCHAR2,
      p_account_id     IN  VARCHAR2,
      p_threshold_flag IN  VARCHAR2 DEFAULT 'Y',
      p_value_date     IN  DATE,
      p_current_date   IN  DATE
   )
   RETURN NUMBER;

   FUNCTION  fn_exchange_rate(
      p_host_id         IN  VARCHAR2,
      p_entity_id       IN  VARCHAR2,
      p_currency_code   IN  VARCHAR2,
      p_movement_type   IN  VARCHAR2
   )
   RETURN NUMBER;

   FUNCTION fnGetNearestBalDate(
      p_host_id      IN  p_account.host_id%TYPE,
      p_entity_id    IN  p_account.entity_id%TYPE,
      p_account_id   IN  p_account.account_id%TYPE,
      p_account_type IN  p_account.account_type%TYPE,
      p_value_date   IN  DATE,
      p_current_date IN  DATE
   )
   RETURN DATE;

   FUNCTION Get_Days_Before_Today
   RETURN PLS_INTEGER;

   FUNCTION Get_Days_After_Today
   RETURN PLS_INTEGER;

   FUNCTION Get_Days_After_Today_Books
   RETURN PLS_INTEGER;

   PROCEDURE SP_UPDATE_WORKFLOW_NEXT_DAYS;

   FUNCTION fn_get_preadvice_amounts (P_HOST_ID       P_ACCOUNT.HOST_ID%TYPE,
                                      P_ENTITY_ID     P_ACCOUNT.ENTITY_ID%TYPE,
                                      P_ACCOUNT_ID    P_ACCOUNT.ACCOUNT_ID%TYPE,
                                      P_VALUE_DATE    DATE)
   RETURN NUMBER;
   
END PKG_PREDICT_JOBS;
/
CREATE OR REPLACE PACKAGE BODY PKG_PREDICT_JOBS AS
/*
Modification History
****************************************************************************************************
Release  Who          Date     Description
-------- ------------ -------- ---------------------------------------------------------------------
1069     <USER>        <GROUP>/09/22  Mantis 3487: Sweeping: Predicted balances used by sweeping should be consistent with those in monitors
1068     Rchatbouri   10/02/22 Mantis 5702: Loro/Predicted Balance Contribution: Allow Account balance contribution to deviate from default during a defined period.
1068     Rchatbouri   28/01/22 Mantis 5583: Allow cross-entity sweeps to be created
...
... Three most recent entries only; see package spec for full history
****************************************************************************************************
*/

   EX_MAT EX_MATRIX;
   -- Process names : These are used in appropriate PRIMARY/SECONDARY process
   vMonitorPopulateData s_process.process_name%TYPE := 'MONITORS: POPULATE DATA';
   vSweepPopulateData   s_process.process_name%TYPE := 'SWEEPING: POPULATE DATA';
   vGroupPopulateData   s_process.process_name%TYPE := 'GROUP MONITORS: POPULATE DATA';
   vHostID              s_host.host_id%TYPE := global_var.fn_get_host;

   PROCEDURE sp_populate_data IS
      v_value_date        DATE;
      v_current_date      DATE;
      v_system_date       DATE := GLOBAL_VAR.SYS_DATE;
      v_entity_id         s_entity.entity_id%TYPE;
      v_db_sid            s_entity_process_status.database_session%TYPE;
      v_process_disabled  VARCHAR2(1) := 'N';
      vErrorLoc           VARCHAR2(10);

      v_source            VARCHAR(200);
      -- use when row lock exception raised
      ROW_LOCKED          EXCEPTION;
      PRAGMA              EXCEPTION_INIT(ROW_LOCKED, -54);

      -- use when error raise in forall
      vForAllExcep        EXCEPTION;
      PRAGMA              EXCEPTION_INIT(vForAllExcep, -24381);

      -- use to capture other than dup_val_index exception
      vOtherExceptions    EXCEPTION;
      PRAGMA              EXCEPTION_INIT(vOtherExceptions, -20001);
      v_error_count       PLS_INTEGER;
      v_sql_code          VARCHAR2(15);
      v_limit             PLS_INTEGER                 := 1000; -- bulk fetch limit
      vHostID             s_host.host_id%TYPE         := global_var.fn_get_host;

      CURSOR cr_populate_data(p_entity_id VARCHAR2, p_value_date DATE, p_current_date DATE) IS
      SELECT host_id,
             entity_id,
             currency_code,
             account_id,
             valuedate,
             account_name,
             (sodintbal + predintbal) - DECODE (forecast_sod_basis_preferred, 'E', openunexptdbal, 0) predbalance,
             unstldbal,
             unexptdbal,
             (sodlorobal + predlorobal) lorobalance,
             (sodextbal + predextbal) extbalance,
             sodintbal,
             openunexptdbal,
             account_class,
             opn_unex_adjust,
             account_type,
             monitor,
             monitor_sum,
             link_account_id,
             account_priority_order,
             currency_priority_order,
             holidayflag,
             auto_open_unsettled,
             auto_open_unexpected,
             bv_adjust_basis_forecast,
             unstldbalthrsh,
             unexptdbalthrsh,
             bv_adjust_basis_external,
             sodextbal,
             future_balance_method,
             forecast_sod_basis_preferred,
             sweep_days,
             cut_off,
             account_status_flag,
             preadvices,
             this_entity_incl_bal_flag,
             this_entity_incl_from,
             this_entity_incl_to,
             servicing_entity_id,
             acc_name_in_svc_entity,
             svc_entity_incl_bal_flag,
             svc_entity_incl_from,
             svc_entity_incl_to
        FROM (SELECT a.host_id,
                     a.entity_id,
                     a.currency_code,
                     a.account_id,
                     TRUNC(p_value_date) valuedate,
                     a.account_name,
                     fn_get_int_sod_future_adj(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        a.account_type,
                        a.bv_adjust_basis_forecast,
                        a.future_balance_method,
                        p_value_date,
                        p_current_date
                     ) sodintbal,
                     fn_get_predict_balance(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        a.forecast_sod_basis_preferred,
                        p_value_date,
                        p_current_date
                     ) predintbal,
                     fn_get_unsettled_balance(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        'N',
                        p_value_date,
                        p_current_date
                     ) unstldbal,
                     fn_get_unexpected_balance(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        'N',
                        p_value_date,
                        p_current_date
                     ) unexptdbal,
                     fn_get_unsettled_balance(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        'Y',
                        p_value_date,
                        p_current_date
                     ) unstldbalthrsh,
                     fn_get_unexpected_balance(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        'Y',
                        p_value_date,
                        p_current_date
                     ) unexptdbalthrsh,
                     fn_get_loro_sod_future_adj(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        a.account_type,
                        a.account_class,
                        A.this_entity_incl_bal_flag,
                        a.bv_adjust_basis_forecast,
                        a.future_balance_method,
                        p_value_date,
                        p_current_date
                     ) sodlorobal,
                     fn_get_predict_balance_loro(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        a.forecast_sod_basis_preferred,
                        a.account_class,
                        a.this_entity_incl_bal_flag,
                        p_value_date,
                        p_current_date
                     ) predlorobal,
                     fn_get_ext_sod_future_adj(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        a.account_type,
                        a.bv_adjust_basis_external,
                        a.future_balance_method,
                        p_value_date,
                        p_current_date
                     ) sodextbal,
                     fn_get_predict_balance_ext(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        p_value_date
                     ) predextbal,
                     fn_get_open_unexpected_balance(
                        a.host_id,
                        a.entity_id,
                        a.currency_code,
                        a.account_id,
                        a.account_class,
                        p_value_date,
                        p_current_date
                     ) openunexptdbal,
                     this_entity_incl_bal_flag,
                     this_entity_incl_from,
                     this_entity_incl_to,
                     servicing_entity_id,
                     acc_name_in_svc_entity,
                     svc_entity_incl_bal_flag,
                     svc_entity_incl_from,
                     svc_entity_incl_to,
                     a.account_class,
                     a.auto_open_unsettled,
                     a.auto_open_unexpected,
                     a.bv_adjust_basis_forecast,
                     a.account_type,
                     a.monitor,
                     a.monitor_sum,
                     a.link_account_id,
                     a.priority_order account_priority_order,
                     b.priority_order currency_priority_order,
                     pkg_non_workday.getdatestatus(
                        'CCY_MONITOR',
                        p_value_date,
                        a.entity_id,
                        a.account_id,
                        a.currency_code
                     ) holidayflag,
                     a.bv_adjust_basis_external,
                     a.future_balance_method,
                     a.forecast_sod_basis_preferred,
                     a.sweep_days,
                     a.cut_off,
                     a.account_status_flag,
                     NULL opn_unex_adjust,
                     fn_get_preadvice_amounts (a.host_id, a.entity_id, a.account_id, p_value_date) preadvices
                FROM p_account a, s_currency b
               WHERE a.entity_id = p_entity_id
                 AND a.account_status_flag IN ('O', 'B', 'C')
                 AND a.account_id <> '*'
                 AND a.currency_code <> '*'
                 AND DECODE(a.account_status_flag,'C',
                     DECODE(a.monitor,'Y',
                     DECODE(a.link_account_id,NULL,'Y','N'),'N'),'Y')='Y'
                 AND a.host_id = b.host_id
                 AND a.entity_id = b.entity_id
                 AND a.currency_code = b.currency_code);

      TYPE fetch_cur IS TABLE OF cr_populate_data%ROWTYPE;
      v_fet_cur               fetch_cur;
      vIndex                  PLS_INTEGER;
   BEGIN
      vErrorLoc := '10';
      INSERT INTO s_job_master(job_date,
                               execution_flag
                              )
                         VALUES(TRUNC(global_var.sys_date),
                                'Y'
                               );
      COMMIT;

      -- Start process for each entity
      FOR v_EntRecTyp IN pkg_entity_process.ent_cv(vMonitorPopulateData)
      LOOP
         v_current_date       := TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_system_date, v_EntRecTyp.entity_id));
         v_value_date         := v_current_date - Get_Days_Before_Today();
         v_entity_id          := v_EntRecTyp.entity_id;
         v_process_disabled   := 'N';

         vErrorLoc := '20';
         -- get the status for the supplied entity (Determine if process is to execute for this entity)
         IF pkg_entity_process.FnIsProcessToRun(v_EntRecTyp.entity_id,
                                                v_EntRecTyp.run_time,
                                                v_EntRecTyp.process_name,
                                                'PRIMARY')
         THEN
            vErrorLoc := '30';
            INSERT INTO s_system_log ( system_seq_no,
                                       host_id,
                                       log_date,
                                       user_id,
                                       ip_address,
                                       process,
                                       action,
                                       update_date,
                                       update_user
                                      )
                               VALUES ( s_system_log_sequence.NEXTVAL,                                 -- SYSTEM_SEQ_NO
                                        vHostID,                                                       -- HOST_ID
                                        global_var.sys_date,                                           -- LOG_DATE
                                        CONST_SYSTEM,                                                  -- USER_ID
                                        CONST_DBSERVER,                                                -- IP_ADDRESS
                                        'Monitors: Populate Data for Entity '||v_EntRecTyp.entity_id,  -- PROCESS
                                        'Start',                                                       -- ACTION
                                        global_var.sys_date,                                           -- UPDATE_DATE
                                        CONST_SYSTEM                                                   -- UPDATE_USER
                                      );
            COMMIT;
            vErrorLoc := '40';

            -- Get Oracle SID (session id) for the session doing the work.
            v_db_sid := SYS_CONTEXT('USERENV','SID');
            vErrorLoc := '50';

            -- Update the process status as 'RUNNING'
            pkg_entity_process.SpUpdBeginStatus(
               v_EntRecTyp.process_name,
               v_EntRecTyp.entity_id,
               'R',
               v_db_sid
            );

            vErrorLoc := '60';
            -- update heart beat of the entity process
            pkg_entity_process.SpUpdHeartbeat(
               v_EntRecTyp.process_name,
               v_EntRecTyp.entity_id
            );
            vErrorLoc := '70';
            -- Check the disable status of the process, if disable is requested then
            -- update the status as disabled for this entity and process next
            IF pkg_entity_process.FnIsProcessToDisable(v_EntRecTyp.process_name,v_EntRecTyp.entity_id) THEN
               vErrorLoc := '80';
               -- Update the process status as 'DISABLE'
               pkg_entity_process.SpUpdBeginStatus(
                  v_EntRecTyp.process_name,
                  v_EntRecTyp.entity_id,
                  'D',
                  null
               );

               vErrorLoc := '90';
               -- Update the last run status as 'DISABLE'
               pkg_entity_process.SpUpdEndStatus(
                  v_EntRecTyp.process_name,
                  v_EntRecTyp.entity_id,
                  'D'
               );
            ELSE
               vErrorLoc := '100';
               -- Update process monitor flag status
               UPDATE p_account a
                  SET a.process_monitor_flag = 'N'
                WHERE a.process_monitor_flag != 'N'
                  AND a.host_id   = v_EntRecTyp.host_id
                  AND a.entity_id = v_EntRecTyp.entity_id;

              COMMIT;

               vErrorLoc := '110';
               -- Delete all records related for the specific entity
               DELETE FROM p_monitors_temp t
                WHERE t.host_id   = v_EntRecTyp.host_id
                  AND t.entity_id = v_EntRecTyp.entity_id;
               COMMIT;

                -- Process balance records for x days before and y days after today
               FOR r_date IN 1..(Get_Days_Before_Today() + Get_Days_After_Today() + 1) LOOP
                  OPEN cr_populate_data(v_entity_id, v_value_date, v_current_date);
                  LOOP
                     BEGIN
                        BEGIN
                           vErrorLoc := '120';
                           -- Fetching cursor records into v_fet_cur
                           FETCH cr_populate_data BULK COLLECT INTO v_fet_cur LIMIT v_limit;

                           vErrorLoc := '130';
                           IF pkg_entity_process.FnIsProcessToDisable(
                              v_EntRecTyp.process_name,
                              v_EntRecTyp.entity_id
                           ) THEN
                              -- Update the process status as 'DISABLE'
                              vErrorLoc := '140';
                              pkg_entity_process.SpUpdBeginStatus(
                                 v_EntRecTyp.process_name,
                                 v_EntRecTyp.entity_id,
                                 'D',
                                 null
                              );

                              vErrorLoc := '150';
                              -- Update the last run status as 'DISABLE'
                              pkg_entity_process.SpUpdEndStatus(
                                 v_EntRecTyp.process_name,
                                 v_EntRecTyp.entity_id,
                                 'D'
                              );
                              v_process_disabled := 'Y';
                              EXIT;
                           END IF;

                           vErrorLoc := '160';
                           -- Save an exception raised during forall DML operation
                           FORALL j IN 1..v_fet_cur.COUNT SAVE EXCEPTIONS
                              INSERT INTO p_monitors_temp VALUES v_fet_cur(j);

                           -- Exit when no records found in the cursor
                           EXIT WHEN cr_populate_data%NOTFOUND;
                        EXCEPTION
                           WHEN row_locked THEN
                              NULL;

                           -- Handle exceptions thrown in the FORALL loop
                           WHEN vforallexcep THEN
                              -- Taking count of saved exception
                              v_error_count := SQL%BULK_EXCEPTIONS.COUNT;
                              vErrorLoc := '170';

                              FOR J IN 1..v_error_count
                              LOOP
                                 -- Gettting error code of saved exceptions
                                  v_sql_code :=  SUBSTR(SQLERRM(-SQL%BULK_EXCEPTIONS(j).ERROR_CODE),1,9);
                                  vErrorLoc := '180';

                                 -- Checking error code is 'ORA-00001' for DUP_VAL_ON_INDEX exception
                                  IF v_sql_code = 'ORA-00001'
                                  THEN
                                    vIndex := SQL%BULK_EXCEPTIONS(j).ERROR_INDEX;
                                    vErrorLoc := '190';

                                    -- Performing update for exception raised records
                                    NULL;

                                    vErrorLoc := '200';
                                  ELSE
                                    -- other than dup val exception allow to log into error log table
                                    RAISE vOtherExceptions;
                                  END IF;
                              END LOOP;
                        END;
                     EXCEPTION
                        WHEN vOtherExceptions THEN
                           sp_error_log(
                              vHostID,
                              CONST_SYSTEM,
                              CONST_DBSERVER,
                              'PKG_PREDICT_JOBS.SP_POPULATE_DATA ' || v_source || ' Location ' || vErrorLoc,
                              SQLCODE,
                              SQLERRM
                           );

                           -- Update the process status as 'NOT RUNNING'
                           vErrorLoc := '210';
                           pkg_entity_process.SpUpdBeginStatus(
                              v_EntRecTyp.process_name,
                              v_EntRecTyp.entity_id,
                              'N',
                              null
                           );

                           vErrorLoc := '220';
                           -- Update the last run status as 'FAILURE'
                           pkg_entity_process.SpUpdEndStatus(
                              v_EntRecTyp.process_name,
                              v_EntRecTyp.entity_id,
                              'F'
                           );
                     END;
                  END LOOP;
                  COMMIT;
                  CLOSE cr_populate_data;

                  v_value_date := v_value_date + 1;

                  vErrorLoc := '230';
                  -- update heart beat of the entity process
                  pkg_entity_process.SpUpdHeartbeat(
                     v_EntRecTyp.process_name,
                     v_EntRecTyp.entity_id
                  );
               END LOOP;

               IF v_process_disabled = 'N' THEN
                  vErrorLoc := '240';
                  -- Update the process status as 'NOT RUNNING'
                  pkg_entity_process.SpUpdBeginStatus(
                     v_EntRecTyp.process_name,
                     v_EntRecTyp.entity_id,
                     'N',
                     null
                  );
                  vErrorLoc := '250';
                  -- Update the last run status as 'SUCCESS'
                  pkg_entity_process.SpUpdEndStatus(
                     v_EntRecTyp.process_name,
                     v_EntRecTyp.entity_id,
                     'S'
                  );
               END IF;
            END IF;

            INSERT INTO s_system_log (system_seq_no,
                                      host_id,
                                      log_date,
                                      user_id,
                                      ip_address,
                                      process,
                                      action,
                                      update_date,
                                      update_user
                                      )
                               VALUES (s_system_log_sequence.NEXTVAL,                               -- SYSTEM_SEQ_NO
                                      vHostID,                                                      -- HOST_ID
                                      global_var.sys_date,                                          -- LOG_DATE
                                      CONST_SYSTEM,                                                 -- USER_ID
                                      CONST_DBSERVER,                                               -- IP_ADDRESS
                                      'Monitors: Populate Data for Entity '||v_EntRecTyp.entity_id, -- PROCESS
                                      'End',                                                        -- ACTION
                                      global_var.sys_date,                                          -- UPDATE_DATE
                                      CONST_SYSTEM                                                  -- UPDATE_USER
                                     );
            COMMIT;
         END IF;  -- Check the disable status of the process
      END LOOP; -- Entity process cursor

      DELETE FROM s_job_master;
      COMMIT;
   EXCEPTION
      WHEN OTHERS THEN
         DELETE FROM s_job_master;
         COMMIT;

         IF v_entity_id IS NOT NULL THEN
             -- Update the process status as 'NOT RUNNING'
             pkg_entity_process.SpUpdBeginStatus(
                vMonitorPopulateData,
                v_entity_id,
                'N',
                null
             );

             -- Update the last run status as 'FAILURE'
             pkg_entity_process.SpUpdEndStatus(
                vMonitorPopulateData,
                v_entity_id,
                'F'
             );
         END IF;

         sp_error_log(
            vHostID,
            CONST_SYSTEM,
            CONST_DBSERVER,
            'PKG_PREDICT_JOBS.SP_POPULATE_DATA Error Location ' || vErrorLoc,
            SQLCODE,
            SQLERRM
         );
   END sp_populate_data;

   PROCEDURE sp_add_additional_dates(
      p_host_id        IN  VARCHAR2,
      p_entity_id      IN  VARCHAR2,
      p_currency_code  IN  VARCHAR2,
      p_account_id     IN  VARCHAR2,
      p_value_date     IN  DATE,
      p_current_date   IN  DATE
   )
   IS
      CURSOR cr_get_account_id
      IS
         SELECT a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                p_value_date balance_date,
                a.account_name,
                a.this_entity_incl_bal_flag,
                a.this_entity_incl_from,
                a.this_entity_incl_to,
                a.acc_name_in_svc_entity,
                a.svc_entity_incl_bal_flag,
                a.svc_entity_incl_from,
                a.svc_entity_incl_to,
                a.account_class,
                a.auto_open_unsettled,
                a.auto_open_unexpected,
                a.bv_adjust_basis_forecast,
                a.monitor,
                a.monitor_sum,
                a.account_type,
                a.priority_order account_priority_order,
                a.link_account_id,
                a.forecast_sod_basis_preferred,
                (
                  fn_get_int_sod_future_adj (
                                      a.host_id,
                                      a.entity_id,
                                      a.currency_code,
                                      a.account_id,
                                      a.account_type,
                                      a.bv_adjust_basis_forecast,
                                      a.future_balance_method,
                                      p_value_date,
                                      p_current_date
                                     )
                ) start_balance,
                 fn_get_open_unexpected_balance(
                                             a.host_id,
                                             a.entity_id,
                                             a.currency_code,
                                             a.account_id,
                                             a.account_class,
                                             p_value_date,
                                             p_current_date

                ) open_unexpected_balance,
                (
                 fn_get_predict_balance(
                                             a.host_id,
                                             a.entity_id,
                                             a.currency_code,
                                             a.account_id,
                                             a.forecast_sod_basis_preferred,
                                             p_value_date,
                                             p_current_date
                                            )
                )predict_balance,
                (
                 fn_get_unsettled_balance(
                                             a.host_id,
                                             a.entity_id,
                                             a.currency_code,
                                             a.account_id,
                                             'N',
                                             p_value_date,
                                             p_current_date
                                            )
                ) unsettled_balance,
                (
                 fn_get_unexpected_balance(
                                             a.host_id,
                                             a.entity_id,
                                             a.currency_code,
                                             a.account_id,
                                             'N',
                                             p_value_date,
                                             p_current_date
                                            )
                ) unexpected_balance,
                (
                 fn_get_unsettled_balance(
                                             a.host_id,
                                             a.entity_id,
                                             a.currency_code,
                                             a.account_id,
                                             'Y',
                                             p_value_date,
                                             p_current_date
                                            )
                ) abve_thrshold_unsetled_balance,
                (
                 fn_get_unexpected_balance(
                                             a.host_id,
                                             a.entity_id,
                                             a.currency_code,
                                             a.account_id,
                                             'Y',
                                             p_value_date,
                                             p_current_date
                                            )
                ) abve_thrshold_unexpctd_balance,
                (
                 fn_get_loro_sod_future_adj(
                                             a.host_id,
                                             a.entity_id,
                                             a.currency_code,
                                             a.account_id,
                                             a.account_type,
                                             a.account_class,
                                             a.this_entity_incl_bal_flag,
                                             a.bv_adjust_basis_forecast,
                                             a.future_balance_method,
                                             p_value_date,
                                             p_current_date
                                            )
                ) start_bal_loro,
                (fn_get_predict_balance_loro(
                                             a.host_id,
                                             a.entity_id,
                                             a.currency_code,
                                             a.account_id,
                                             a.forecast_sod_basis_preferred,
                                             a.account_class,
                                             a.this_entity_incl_bal_flag,
                                             p_value_date,
                                             p_current_date
                                            )
                ) predict_bal_loro,
                (
                 fn_get_ext_sod_future_adj(
                                             a.host_id,
                                             a.entity_id,
                                             a.currency_code,
                                             a.account_id,
                                             a.account_type,
                                             a.bv_adjust_basis_external,
                                             a.future_balance_method,
                                             p_value_date,
                                             p_current_date
                                            )
                ) start_bal_ext,
                (fn_get_predict_balance_ext(
                                             a.host_id,
                                             a.entity_id,
                                             a.currency_code,
                                             a.account_id,
                                             p_value_date
                                            )
                ) predict_bal_ext,
                b.priority_order currency_priority_order,
                pkg_non_workday.getdatestatus('CCY_MONITOR',
                                               p_value_date,
                                               a.entity_id,
                                               a.account_id,
                                               a.currency_code
                                    ) holiday_flag,
                bv_adjust_basis_external,
                a.future_balance_method,
                a.sweep_days,
                a.cut_off,
                a.account_status_flag,
                fn_get_preadvice_amounts (a.host_id, a.entity_id, a.account_id, p_value_date) preadvices
           FROM p_account a,
                s_currency b
          WHERE a.host_id = p_host_id
            AND a.entity_id = p_entity_id
            AND NVL(p_account_id, 'All') In ('All', a.account_id)
            AND a.currency_code = p_currency_code
            AND a.account_status_flag IN ('O','B','C')
            AND DECODE(a.account_status_flag,'C',
                DECODE(a.monitor,'Y',
                DECODE(a.link_account_id,NULL,'Y','N'),'N'),'Y')='Y'
            AND a.host_id = b.host_id
            AND a.entity_id = b.entity_id
            AND a.currency_code = b.currency_code;
   BEGIN

      FOR v_EntRecTyp IN pkg_entity_process.ent_cv(vMonitorPopulateData)
      LOOP

         IF pkg_entity_process.FnIsProcessToRun(v_EntRecTyp.entity_id,
                                                v_EntRecTyp.run_time,
                                                v_EntRecTyp.process_name,
                                                'SECONDARY')
         THEN
            FOR r_get_account_id IN cr_get_account_id
            LOOP
                INSERT INTO
                p_monitors_temp(
                                host_id,  -- 1
                                entity_id, -- 2
                                currency_code, -- 3
                                account_id,   -- 4
                                balance_date, -- 5
                                account_name, -- 6
                                predict_balance, -- 7
                                unsettled_balance, -- 8
                                unexpected_balance, -- 9
                                abve_thrshold_unsetled_balance,  -- 10
                                abve_thrshold_unexpctd_balance,  -- 11
                                loro_balance,   -- 12
                                external_balance,  -- 13
                                start_balance,  -- 14
                                open_unexpected_bal,  -- 15
                                account_class,  -- 16
                                auto_open_unsettled,  -- 17
                                auto_open_unexpected,  -- 18
                                bv_adjust_basis_forecast,  -- 19
                                account_type,  -- 20
                                monitor,  -- 21
                                monitor_sum,  -- 22
                                link_account_id,  -- 23
                                account_priority_order,  -- 24
                                currency_priority_order,  --25
                                holiday_flag,  -- 26
                                bv_adjust_basis_external,  -- 27
                                start_balance_external, -- 28
                                future_balance_method, -- 29
                                forecast_sod_basis_preferred, -- 30
                                sweep_days, -- 31
                                cut_off, -- 32
                                account_status_flag, -- 33
                                preadvices, --34
                                this_entity_incl_bal_flag, --35
                                this_entity_incl_from, --36
                                this_entity_incl_to, --37
                                acc_name_in_svc_entity, --38
                                svc_entity_incl_bal_flag, --39
                                svc_entity_incl_from, --40
                                svc_entity_incl_to --41
                              )
                      VALUES (
                               r_get_account_id.host_id,   -- 1
                               r_get_account_id.entity_id,  -- 2
                               r_get_account_id.currency_code,  -- 3
                               r_get_account_id.account_id,  -- 4
                               r_get_account_id.balance_date,  -- 5
                               r_get_account_id.account_name,  -- 6
                               (    (r_get_account_id.start_balance + r_get_account_id.predict_balance)
                                  - ( DECODE (r_get_account_id.forecast_sod_basis_preferred, 'E', r_get_account_id.open_unexpected_balance, 0)
                                     )
                                ), -- 7
                               (r_get_account_id.unsettled_balance),  -- 8
                               (r_get_account_id.unexpected_balance), -- 9
                               (r_get_account_id.abve_thrshold_unsetled_balance),  -- 10
                               (r_get_account_id.abve_thrshold_unexpctd_balance),  -- 11
                               (r_get_account_id.start_bal_loro + r_get_account_id.predict_bal_loro), -- 12
                               (r_get_account_id.start_bal_ext + r_get_account_id.predict_bal_ext), -- 13
                               (r_get_account_id.start_balance), -- 14
                               r_get_account_id.open_unexpected_balance,  -- 15
                               r_get_account_id.account_class,  -- 16
                               r_get_account_id.auto_open_unsettled, -- 17
                               r_get_account_id.auto_open_unexpected,  -- 18
                               r_get_account_id.bv_adjust_basis_forecast,  -- 19
                               r_get_account_id.account_type, -- 20
                               r_get_account_id.monitor,  -- 21
                               r_get_account_id.monitor_sum,  -- 22
                               r_get_account_id.link_account_id,  -- 23
                               r_get_account_id.account_priority_order,  -- 24
                               r_get_account_id.currency_priority_order,  -- 25
                               r_get_account_id.holiday_flag,  -- 26
                               r_get_account_id.bv_adjust_basis_external,  -- 27
                               r_get_account_id.start_bal_ext, -- 28
                               r_get_account_id.future_balance_method, -- 29
                               r_get_account_id.forecast_sod_basis_preferred, -- 30
                               r_get_account_id.sweep_days, -- 31
                               r_get_account_id.cut_off, -- 32
                               r_get_account_id.account_status_flag, -- 33
                               r_get_account_id.preadvices, --34
                               r_get_account_id.this_entity_incl_bal_flag, --35
                               r_get_account_id.this_entity_incl_from, --36
                               r_get_account_id.this_entity_incl_to, --37
                               r_get_account_id.acc_name_in_svc_entity, --38
                               r_get_account_id.svc_entity_incl_bal_flag, --39
                               r_get_account_id.svc_entity_incl_from, --40
                               r_get_account_id.svc_entity_incl_to --41
                              );
               COMMIT;
            END LOOP;
         END IF;
      END LOOP;

   EXCEPTION
      WHEN OTHERS THEN
         NULL;
   END sp_add_additional_dates;

   PROCEDURE sp_update_records_priority
   IS
      TYPE t_currency_code IS TABLE OF p_account.currency_code%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_account_name IS TABLE OF p_account.account_name%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_balance_date IS TABLE OF p_balance.balance_date%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_auto_open_unsettled IS TABLE OF p_account.auto_open_unsettled%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_auto_open_unexpected IS TABLE OF p_account.auto_open_unexpected%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_start_balance_option IS TABLE OF p_account.bv_adjust_basis_forecast%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_account_class IS TABLE OF p_account.account_class%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_predict_balance IS TABLE OF p_monitors_temp.predict_balance%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_unexpected_balance IS TABLE OF p_monitors_temp.unexpected_balance%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_unsettled_balance IS TABLE OF p_monitors_temp.unsettled_balance%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_loro_balance IS TABLE OF p_monitors_temp.loro_balance%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_external_balance IS TABLE OF p_monitors_temp.external_balance%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_monitor_sum IS TABLE OF p_account.monitor_sum%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_sum_flag IS TABLE OF p_monitors_temp.monitor_sum%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_account_type IS TABLE OF p_monitors_temp.account_type%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_priority_order IS TABLE OF p_account.priority_order%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_forecast_sod_basis_pref IS TABLE OF p_account.forecast_sod_basis_preferred%TYPE
         INDEX BY BINARY_INTEGER;

       v_host_id                      t_host_id;
       v_entity_id                    t_entity_id;
       v_currency_code                t_currency_code;
       v_account_id                   t_account_id;
       v_balance_date                 t_balance_date;
       v_start_bal_predict            t_external_balance;
       v_predict_bal_predict          t_external_balance;
       v_open_unexpected_bal          t_external_balance;
       v_unexpected_balance           t_external_balance;
       v_unsettled_balance            t_external_balance;
       v_abve_thrs_unexpctd_balance   t_external_balance;
       v_abve_thrs_unstld_balance     t_external_balance;
       v_start_bal_loro               t_external_balance;
       v_loro_balance                 t_loro_balance;
       v_start_bal_ext                t_external_balance;
       v_external_balance             t_external_balance;
       v_preadvices                   t_external_balance;
       v_account_name                 t_account_name;
       v_forecast_sod_basis_preferred t_forecast_sod_basis_pref;
       v_current_date                 DATE := GLOBAL_VAR.SYS_DATE;
       v_currency_code_process        t_currency_code;

      CURSOR cr_account_to_process(p_ent_id s_entity.entity_id%TYPE)
      IS
      SELECT a.host_id, a.entity_id, a.currency_code,
             a.account_id, a.forecast_sod_basis_preferred
        FROM p_account a, s_curr_job_priority c, s_account_job_priority d
       WHERE a.process_monitor_flag != 'N'
         AND c.currency_code = a.currency_code
         AND d.currency_code = c.currency_code
         AND a.account_type = d.account_type
         AND a.account_class = d.account_class
         AND a.entity_id = p_ent_id;

      CURSOR cr_get_account_id_priority(
         p_host_id         IN   p_account.host_id%TYPE,
         p_entity_id       IN   p_account.entity_id%TYPE,
         p_currency_code   IN   p_account.currency_code%TYPE,
         p_account_id      IN   p_account.account_id%TYPE
      )
      IS
      SELECT --/*+ USE_CONCAT */
             a.host_id, a.entity_id, a.currency_code, a.account_id, a.balance_date, a.account_name,
             fn_get_int_sod_future_adj (
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.account_type,
                a.bv_adjust_basis_forecast,
                a.future_balance_method,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) start_balance,
             fn_get_open_unexpected_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.account_class,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) open_unexpected_balance,
             fn_get_predict_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.forecast_sod_basis_preferred,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) predict_balance,
             fn_get_unsettled_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                'N',
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) unsettled_balance,
             fn_get_unexpected_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                'N',
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) unexpected_balance,
             fn_get_unsettled_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                'Y',
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) abve_thrshold_unsetled_balance,
             fn_get_unexpected_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                'Y',
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) abve_thrshold_unexpctd_balance,
             fn_get_loro_sod_future_adj(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.account_type,
                a.account_class,
                a.this_entity_incl_bal_flag,
                a.bv_adjust_basis_forecast,
                a.future_balance_method,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) start_bal_loro,
             fn_get_predict_balance_loro(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.bv_adjust_basis_forecast,
                a.account_class,
                a.this_entity_incl_bal_flag,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) predict_bal_loro,
             fn_get_ext_sod_future_adj(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.account_type,
                a.bv_adjust_basis_external,
                a.future_balance_method,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) start_bal_ext,
             fn_get_predict_balance_ext(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.balance_date
             ) predict_bal_ext,
             fn_get_preadvice_amounts (a.host_id, a.entity_id, a.account_id, TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))) preadvices
        FROM p_monitors_temp a,
             s_currency b,
             s_curr_job_priority c,
             s_account_job_priority d
       WHERE a.host_id = p_host_id
         AND a.entity_id = p_entity_id
         AND a.currency_code = p_currency_code
         AND a.account_id = p_account_id
         AND b.host_id = a.host_id
         AND b.entity_id = a.entity_id
         AND b.currency_code = a.currency_code
         AND c.currency_code = b.currency_code
         AND d.currency_code = c.currency_code
         AND a.account_type = d.account_type
         AND a.account_class = d.account_class
         AND b.currency_code <> '*'
       ORDER BY c.execution_priority, d.execution_priority;
   BEGIN
      -- Start process for each entity
      FOR v_EntRecTyp IN pkg_entity_process.ent_cv(vMonitorPopulateData)
      LOOP
         -- Execute the update job only if conditions for populate job, running for the same entity are NOT satisfied
         IF pkg_entity_process.FnIsProcessToRun(
            v_EntRecTyp.entity_id,
            v_EntRecTyp.run_time,
            v_EntRecTyp.process_name,
            'SECONDARY'
         ) THEN
            OPEN cr_account_to_process(v_EntRecTyp.entity_id);

            FETCH cr_account_to_process
               BULK COLLECT INTO v_host_id_process, v_entity_id_process,
                                 v_currency_code_process, v_account_id_process,
                                 v_forecast_sod_basis_preferred;

            CLOSE cr_account_to_process;

            FOR i IN 1..v_account_id_process.COUNT
            LOOP
               OPEN cr_get_account_id_priority(
                  v_host_id_process (i),
                  v_entity_id_process (i),
                  v_currency_code_process (i),
                  v_account_id_process (i)
               );

               LOOP
                  BEGIN
                     FETCH cr_get_account_id_priority
                     BULK COLLECT INTO v_host_id, v_entity_id, v_currency_code, v_account_id, v_balance_date, v_account_name,
                                       v_start_bal_predict, v_open_unexpected_bal, v_predict_bal_predict, v_unsettled_balance,
                                       v_unexpected_balance, v_abve_thrs_unstld_balance, v_abve_thrs_unexpctd_balance, v_start_bal_loro,
                                       v_loro_balance, v_start_bal_ext, v_external_balance, v_preadvices
                     LIMIT 100;

                     FORALL cur_var IN 1..v_account_id.COUNT
                     UPDATE p_monitors_temp
                        SET predict_balance =
                                  ((v_start_bal_predict (cur_var) + v_predict_bal_predict (cur_var))
                                     - DECODE (v_forecast_sod_basis_preferred(i), 'E', v_open_unexpected_bal (cur_var),0)
                                  ),
                            unsettled_balance = (v_unsettled_balance (cur_var)),
                            unexpected_balance = (v_unexpected_balance (cur_var)),
                            abve_thrshold_unsetled_balance = (v_abve_thrs_unstld_balance (cur_var)),
                            abve_thrshold_unexpctd_balance = (v_abve_thrs_unexpctd_balance (cur_var)),
                            loro_balance = (v_start_bal_loro (cur_var) + v_loro_balance (cur_var)),
                            external_balance = (v_start_bal_ext (cur_var) + v_external_balance (cur_var)),
                            start_balance = (v_start_bal_predict (cur_var)),
                            open_unexpected_bal = (v_open_unexpected_bal (cur_var)),
                            start_balance_external = (v_start_bal_ext (cur_var)),
                            preadvices = (v_preadvices (cur_var))
                      WHERE host_id = v_host_id (cur_var)
                        AND entity_id = v_entity_id (cur_var)
                        AND currency_code = v_currency_code (cur_var)
                        AND account_id = v_account_id (cur_var)
                        AND balance_date = v_balance_date (cur_var);
                     COMMIT;
                     EXIT WHEN cr_get_account_id_priority%NOTFOUND;
                  END;
               END LOOP;

               CLOSE cr_get_account_id_priority;

               -- The below call is used to
               -- update the p_account.process_monitor_flag column by using the above pl/sql table
               -- and handle these using autonomous procedure to avoid row blocking
               PK_APPLICATION.SET_ACCOUNT_PROCESS_FLAG(
                  v_host_id_process (i),
                  v_entity_id_process (i),
                  v_account_id_process (i),
                  PK_APPLICATION.CNST_FLAG_AS_PROCESSED
               );
               COMMIT;
            END LOOP;
         END IF;
      END LOOP;
   EXCEPTION
       WHEN OTHERS
       THEN
       IF cr_account_to_process%ISOPEN
       THEN
           CLOSE cr_account_to_process;
       END IF;

       IF cr_get_account_id_priority%ISOPEN
       THEN
           CLOSE cr_get_account_id_priority;
       END IF;

       sp_error_log(
         vHostID,
         CONST_SYSTEM,
         CONST_DBSERVER,
         'PKG_PREDICT_JOBS.SP_UPDATE_RECORDS_PRIORITY',
         SQLCODE,
         SQLERRM
      );
   END sp_update_records_priority;

   PROCEDURE sp_update_records
   IS
      TYPE t_currency_code is table OF p_account.currency_code%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_account_name IS TABLE OF p_account.account_name%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_balance_date IS TABLE OF p_balance.balance_date%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_account_class IS TABLE OF p_account.account_class%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_predict_balance IS TABLE OF p_monitors_temp.predict_balance%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_unexpected_balance IS TABLE OF p_monitors_temp.unexpected_balance%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_unsettled_balance IS TABLE OF p_monitors_temp.unsettled_balance%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_loro_balance IS TABLE OF p_monitors_temp.loro_balance%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_external_balance IS TABLE OF p_monitors_temp.external_balance%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_monitor_sum IS TABLE OF p_account.monitor_sum%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_sum_flag IS TABLE OF p_monitors_temp.monitor_sum%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_account_type IS TABLE OF p_monitors_temp.account_type%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_priority_order IS TABLE OF p_account.priority_order%TYPE
         INDEX BY BINARY_INTEGER;

      TYPE t_forecast_sod_basis_pref IS TABLE OF p_account.forecast_sod_basis_preferred%TYPE
         INDEX BY BINARY_INTEGER;

      v_host_id                      t_host_id;
      v_entity_id                    t_entity_id;
      v_currency_code                t_currency_code;
      v_account_id                   t_account_id;
      v_balance_date                 t_balance_date;
      v_start_bal_predict            t_external_balance;
      v_predict_bal_predict          t_external_balance;
      v_open_unexpected_bal          t_external_balance;
      v_unexpected_balance           t_external_balance;
      v_unsettled_balance            t_external_balance;
      v_abve_thrs_unsetld_balance    t_external_balance;
      v_abve_thrs_unexpctd_balance   t_external_balance;
      v_start_bal_loro               t_external_balance;
      v_loro_balance                 t_loro_balance;
      v_start_bal_ext                t_external_balance;
      v_external_balance             t_external_balance;
      v_preadvices                   t_external_balance;
      v_account_name                 t_account_name;
      v_current_date                 DATE := GLOBAL_VAR.SYS_DATE;
      v_currency_code_process        t_currency_code;
      v_forecast_sod_basis_preferred t_forecast_sod_basis_pref;

      CURSOR cr_account_to_process(p_ent_id s_entity.entity_id%TYPE)
      IS
      SELECT a.host_id, a.entity_id, a.currency_code, a.account_id, a.forecast_sod_basis_preferred
        FROM p_account a
       WHERE a.process_monitor_flag != 'N'
         AND a.entity_id = p_ent_id
         AND NOT EXISTS ( SELECT currency_code
                            FROM s_account_job_priority c
                           WHERE c.currency_code = a.currency_code
                             AND c.account_type = a.account_type
                             AND c.account_class = a.account_class
                        );

      CURSOR cr_get_account_id(
         p_host_id         IN   p_account.host_id%TYPE,
         p_entity_id       IN   p_account.entity_id%TYPE,
         p_currency_code   IN   p_account.currency_code%TYPE,
         p_account_id      IN   p_account.account_id%TYPE
      )
      IS
      SELECT a.host_id, a.entity_id, a.currency_code, a.account_id, a.balance_date, a.account_name,
             fn_get_int_sod_future_adj(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.account_type,
                a.bv_adjust_basis_forecast,
                a.future_balance_method,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) start_balance,
             fn_get_open_unexpected_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.account_class,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) open_unexpected_balance,
             fn_get_predict_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.forecast_sod_basis_preferred,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) predict_balance,
             fn_get_unsettled_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                'N',
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) unsettled_balance,
             fn_get_unexpected_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                'N',
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) unexpected_balance,
             fn_get_unsettled_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                'Y',
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) abve_thrshold_unsetled_balance,
             fn_get_unexpected_balance(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                'Y',
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) abve_thrshold_unexpctd_balance,
             fn_get_loro_sod_future_adj(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.account_type,
                a.account_class,
                a.this_entity_incl_bal_flag,
                a.bv_adjust_basis_forecast,
                a.future_balance_method,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) start_bal_loro,
             fn_get_predict_balance_loro(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.forecast_sod_basis_preferred,
                a.account_class,
                a.this_entity_incl_bal_flag,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) predict_bal_loro,
             fn_get_start_bal_external(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.account_type,
                a.bv_adjust_basis_external,
                a.balance_date,
                TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))
             ) start_bal_ext,
             fn_get_predict_balance_ext(
                a.host_id,
                a.entity_id,
                a.currency_code,
                a.account_id,
                a.balance_date
             ) predict_bal_ext,
             fn_get_preadvice_amounts (a.host_id, a.entity_id, a.account_id, TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(v_current_date, a.entity_id))) preadvices
        FROM p_monitors_temp a
       WHERE a.host_id = p_host_id
         AND a.entity_id = p_entity_id
         AND a.currency_code = p_currency_code
         AND a.account_id = p_account_id
         AND NOT EXISTS ( SELECT currency_code
                            FROM s_account_job_priority c
                           WHERE c.currency_code = a.currency_code
                             AND c.account_type = a.account_type
                             AND c.account_class = a.account_class
                        );
   BEGIN
      -- Start process for each entity
      FOR v_EntRecTyp IN pkg_entity_process.ent_cv(vMonitorPopulateData)
      LOOP
         -- Execute the update job only if conditions for populate job, running for the same entity are NOT satisfied
         IF pkg_entity_process.FnIsProcessToRun(
            v_EntRecTyp.entity_id,
            v_EntRecTyp.run_time,
            v_EntRecTyp.process_name,
            'SECONDARY'
         )
         THEN
            OPEN cr_account_to_process(v_EntRecTyp.entity_id);

            FETCH cr_account_to_process
            BULK COLLECT INTO v_host_id_process, v_entity_id_process,
                              v_currency_code_process, v_account_id_process,
                              v_forecast_sod_basis_preferred;

            CLOSE cr_account_to_process;

            FOR i IN 1..v_account_id_process.count
            LOOP
               OPEN cr_get_account_id (v_host_id_process (i),
                                       v_entity_id_process (i),
                                       v_currency_code_process (i),
                                       v_account_id_process (i)
                                      );

               LOOP
                   BEGIN
                       FETCH cr_get_account_id
                       BULK COLLECT INTO
                          v_host_id, v_entity_id, v_currency_code, v_account_id, v_balance_date, v_account_name,
                          v_start_bal_predict, v_open_unexpected_bal, v_predict_bal_predict, v_unsettled_balance,
                          v_unexpected_balance, v_abve_thrs_unsetld_balance, v_abve_thrs_unexpctd_balance,
                          v_start_bal_loro, v_loro_balance, v_start_bal_ext, v_external_balance, v_preadvices
                       LIMIT 100;

                       FORALL cur_var IN 1..v_account_id.COUNT
                       UPDATE p_monitors_temp
                          SET predict_balance =
                               (  (v_start_bal_predict (cur_var) + v_predict_bal_predict (cur_var))
                                - DECODE (v_forecast_sod_basis_preferred(i), 'E', v_open_unexpected_bal (cur_var),0)
                               ),
                              unsettled_balance = (v_unsettled_balance (cur_var)),
                              unexpected_balance = (v_unexpected_balance (cur_var)),
                              abve_thrshold_unsetled_balance = (v_abve_thrs_unsetld_balance (cur_var)),
                              abve_thrshold_unexpctd_balance = (v_abve_thrs_unexpctd_balance (cur_var)),
                              loro_balance = (v_start_bal_loro (cur_var) + v_loro_balance (cur_var)),
                              external_balance = (v_start_bal_ext (cur_var) + v_external_balance (cur_var)),
                              start_balance = (v_start_bal_predict (cur_var)),
                              open_unexpected_bal = (v_open_unexpected_bal (cur_var)),
                              start_balance_external = (v_start_bal_ext (cur_var)),
                              preadvices = (v_preadvices (cur_var))
                        WHERE host_id = v_host_id (cur_var)
                          AND entity_id = v_entity_id (cur_var)
                          AND currency_code = v_currency_code (cur_var)
                          AND account_id = v_account_id (cur_var)
                          AND balance_date = v_balance_date (cur_var);
                       COMMIT;
                       EXIT WHEN cr_get_account_id%NOTFOUND;
                   END;
               END LOOP;

               CLOSE cr_get_account_id;

               -- The below call is used to
               -- update the p_account.process_monitor_flag column by using the above pl/sql table
               -- and handle these using SET_ACCOUNT_PROCESS_FLAG procedure to avoid row blocking
               PK_APPLICATION.SET_ACCOUNT_PROCESS_FLAG(
                  v_host_id_process (i),
                  v_entity_id_process (i),
                  v_account_id_process (i),
                  PK_APPLICATION.CNST_FLAG_AS_PROCESSED
               );
               COMMIT;
            END LOOP;
         END IF;
      END LOOP;
   EXCEPTION
      WHEN OTHERS
      THEN
         IF cr_account_to_process%isopen
         THEN
            CLOSE cr_account_to_process;
         END IF;

         IF cr_get_account_id%isopen
         THEN
            CLOSE cr_get_account_id;
         END IF;

         SP_ERROR_LOG(
            vHostID,
            CONST_SYSTEM,
            CONST_DBSERVER,
            'PKG_PREDICT_JOBS.SP_UPDATE_RECORDS',
            SQLCODE,
            SQLERRM
         );
   END sp_update_records;

   PROCEDURE sp_populate_sweep_data IS
      v_current_date          DATE := TRUNC(global_var.sys_date);
      c_value_date            DATE ;
      v_predict_balance       p_sweep_monitor.predict_balance%TYPE;
      v_external_balance      p_sweep_monitor.external_balance%TYPE;
      v_entity_id             s_entity.entity_id%TYPE;
      v_db_sid                s_entity_process_status.database_session%TYPE;
      v_process_disabled      VARCHAR2(1) := 'N';
      vErrorLoc               VARCHAR2(10);

      CURSOR cr_account(p_ent_id s_entity.entity_id%TYPE)
      IS
         SELECT host_id, entity_id, currency_code, account_id, account_type,
                account_name, account_class, manual_sweep_flag,
                CASE
                   WHEN use_sub_acc_time_only = 'N'
                      THEN main_cut_off
                   ELSE cut_off
                END AS cut_off,
                main_account_id,
                NVL (DECODE (target_balance_sign,
                             'C', target_balance,
                             'D', -target_balance
                            ),
                     0
                    ) target_balance,
                bv_adjust_basis_forecast,
                ext_sod_basis_preferred,
                bv_adjust_basis_external,
                forecast_sod_basis_preferred,
                future_balance_method,
                currency_priority_order,
                min_sweep_amount,
                -- Preserve existing functionality by reversing the sign (and allow for past
                -- value by not simplying applying the abs function)
                CASE
                   WHEN use_sub_acc_time_only = 'N'
                      THEN -LEAST(main_sweep_days, sweep_days)
                   ELSE -sweep_days
                END AS sweep_days
           FROM (SELECT a.*, b.priority_order currency_priority_order,
                        (SELECT sweep_days
                           FROM p_account c
                          WHERE c.host_id = a.host_id
                            AND c.entity_id = a.entity_id
                            AND c.account_id = a.main_account_id) main_sweep_days,
                        (SELECT cut_off
                           FROM p_account c
                          WHERE c.host_id = a.host_id
                            AND c.entity_id = a.entity_id
                            AND c.account_id = a.main_account_id) main_cut_off,
                        (SELECT use_sub_acc_timing_only
                           FROM p_account c
                          WHERE c.host_id = a.host_id
                            AND c.entity_id = a.entity_id
                            AND c.account_id = a.main_account_id) use_sub_acc_time_only
                   FROM p_account a, s_currency b
                  WHERE a.host_id = b.host_id
                    AND a.entity_id = b.entity_id
                    AND a.currency_code = b.currency_code
                    AND a.entity_id = p_ent_id
                    AND b.currency_code <> '*'
                    AND a.account_level = 'S'
                    AND a.main_account_id IS NOT NULL
                    AND (manual_sweep_flag != 'N' OR auto_sweep_switch != 'N'))
         ORDER BY currency_code;
   BEGIN
       vErrorLoc := '10';
       -- Start process for each entity
       FOR v_EntRecTyp IN pkg_entity_process.ent_cv(vSweepPopulateData)
       LOOP
           v_entity_id     := v_EntRecTyp.entity_id;
           v_process_disabled := 'N';
           vErrorLoc := '20';
           -- get the status for the supplied entity (Determine if process is to execute for this entity)
           IF pkg_entity_process.FnIsProcessToRun(v_EntRecTyp.entity_id,
                                                  v_EntRecTyp.run_time,
                                                  v_EntRecTyp.process_name,
                                                  'PRIMARY')
           THEN
               INSERT INTO S_SYSTEM_LOG ( system_seq_no,
                                          host_id,
                                          log_date,
                                          user_id,
                                          ip_address,
                                          process,
                                          action,
                                          update_date,
                                          update_user
                                        )
                                 VALUES ( s_system_log_sequence.NEXTVAL,                              -- SYSTEM_SEQ_NO
                                          vHostID,                                                    -- HOST_ID
                                          global_var.sys_date,                                        -- LOG_DATE
                                          CONST_SYSTEM,                                               -- USER_ID
                                          CONST_DBSERVER,                                             -- IP_ADDRESS
                                          'Sweep: Populate Data for Entity '||v_EntRecTyp.entity_id,  -- PROCESS
                                          'Start',                                                    -- ACTION
                                          global_var.sys_date,                                        -- UPDATE_DATE
                                          CONST_SYSTEM                                                -- UPDATE_USER
                                     );
               COMMIT;
               vErrorLoc := '30';
               -- Get Oracle SID (session id) for the session doing the work
               v_db_sid := SYS_CONTEXT('USERENV','SID');
               vErrorLoc := '40';
               -- Update the process status as 'RUNNING'
               pkg_entity_process.SpUpdBeginStatus(v_EntRecTyp.process_name,
                                                   v_EntRecTyp.entity_id,
                                                   'R',
                                                   v_db_sid);
               vErrorLoc := '50';
               -- update heart beat of the entity process
               pkg_entity_process.SpUpdHeartbeat(v_EntRecTyp.process_name,
                                                 v_EntRecTyp.entity_id);
               vErrorLoc := '60';
               -- Check the disable status of the process, if diable is requested then
               -- update the status as diabled for this entity and process next
               IF pkg_entity_process.FnIsProcessToDisable(v_EntRecTyp.process_name,v_EntRecTyp.entity_id) THEN
                   vErrorLoc := '70';
                   -- Update the process status as 'DISABLE'
                   pkg_entity_process.SpUpdBeginStatus(v_EntRecTyp.process_name,
                                                       v_EntRecTyp.entity_id,
                                                       'D',
                                                       null);
                   vErrorLoc := '80';
                   -- Update the last run status as 'DISABLE'
                   pkg_entity_process.SpUpdEndStatus(v_EntRecTyp.process_name,
                                                     v_EntRecTyp.entity_id,
                                                     'D');

               ELSE
                   vErrorLoc := '90';
                   DELETE FROM p_sweep_monitor
                         WHERE host_id = vHostID
                           AND entity_id = v_EntRecTyp.entity_id;

                   FOR r_get_account_id IN cr_account(v_EntRecTyp.entity_id) LOOP
                       -- Check the disable status of the process, if diable is requested then
                       -- update the status as diabled for this entity and process next
                       vErrorLoc := '100';
                       IF pkg_entity_process.FnIsProcessToDisable(
                           v_EntRecTyp.process_name,
                           v_EntRecTyp.entity_id
                       ) THEN
                           -- Update the process status as 'DISABLE'
                           pkg_entity_process.SpUpdBeginStatus(
                              v_EntRecTyp.process_name,
                              v_EntRecTyp.entity_id,
                              'D',
                              null
                           );
                           vErrorLoc := '110';
                           -- Update the last run status as 'DISABLE'
                           pkg_entity_process.SpUpdEndStatus(
                              v_EntRecTyp.process_name,
                              v_EntRecTyp.entity_id,
                              'D'
                           );
                           v_process_disabled := 'Y';
                           EXIT;
                       END IF;
                       vErrorLoc := '120';
                       c_value_date := sweeping_process.get_sweep_date(
                                                                       r_get_account_id.host_id,
                                                                       r_get_account_id.entity_id,
                                                                       r_get_account_id.main_account_id,
                                                                       r_get_account_id.entity_id,
                                                                       r_get_account_id.account_id,
                                                                       v_current_date,
                                                                       r_get_account_id.sweep_days
                                                                      );
                       vErrorLoc := '130';
                       v_predict_balance := (  fn_get_predict_balance(
                                                                      r_get_account_id.host_id,
                                                                      r_get_account_id.entity_id,
                                                                      r_get_account_id.currency_code,
                                                                      r_get_account_id.account_id,
                                                                      r_get_account_id.forecast_sod_basis_preferred,
                                                                      c_value_date,
                                                                      v_current_date
                                                                     )
                                             + fn_get_int_sod_future_adj (
                                                                          r_get_account_id.host_id,
                                                                          r_get_account_id.entity_id,
                                                                          r_get_account_id.currency_code,
                                                                          r_get_account_id.account_id,
                                                                          r_get_account_id.account_type,
                                                                          r_get_account_id.bv_adjust_basis_forecast,
                                                                          r_get_account_id.future_balance_method,
                                                                          c_value_date,
                                                                          v_current_date
                                                                         )
                                             - CASE
                                                 WHEN r_get_account_id.forecast_sod_basis_preferred =  'E'
                                                 THEN PKG_PREDICT_JOBS.fn_get_open_unexpected_balance(
                                                                          r_get_account_id.host_id,
                                                                          r_get_account_id.entity_id,
                                                                          r_get_account_id.currency_code,
                                                                          r_get_account_id.account_id,
                                                                          r_get_account_id.account_class,
                                                                          c_value_date,
                                                                          v_current_date
                                                                          )
                                                 ELSE 0
                                               END   
                                            );
                       vErrorLoc := '140';
                       v_external_balance := (  fn_get_predict_balance_ext(
                                                                           r_get_account_id.host_id,
                                                                           r_get_account_id.entity_id,
                                                                           r_get_account_id.currency_code,
                                                                           r_get_account_id.account_id,
                                                                           c_value_date
                                                                          )
                                              + fn_get_ext_sod_future_adj(
                                                                          r_get_account_id.host_id,
                                                                          r_get_account_id.entity_id,
                                                                          r_get_account_id.currency_code,
                                                                          r_get_account_id.account_id,
                                                                          r_get_account_id.account_type,
                                                                          r_get_account_id.bv_adjust_basis_external,
                                                                          r_get_account_id.future_balance_method,
                                                                          c_value_date,
                                                                          v_current_date
                                                                         )
                                             );
                       vErrorLoc := '150';
                       INSERT INTO p_sweep_monitor (
                                                    host_id,
                                                    entity_id ,
                                                    currency_code,
                                                    account_id,
                                                    account_name,
                                                    account_class,
                                                    main_account_id,
                                                    manual_sweep_flag,
                                                    sweep_days,
                                                    cut_off,
                                                    value_date,
                                                    target_balance,
                                                    predict_balance,
                                                    sweep_balance,
                                                    min_sweep_amount,
                                                    currency_priority_order,
                                                    start_balance_option,
                                                    account_type,
                                                    forecast_sod_basis_preferred,
                                                    external_balance
                                                  )
                                           VALUES (
                                                    r_get_account_id.host_id,
                                                    r_get_account_id.entity_id,
                                                    r_get_account_id.currency_code,
                                                    r_get_account_id.account_id,
                                                    r_get_account_id.account_name,
                                                    r_get_account_id.account_class,
                                                    r_get_account_id.main_account_id,
                                                    r_get_account_id.manual_sweep_flag,
                                                    r_get_account_id.sweep_days,
                                                    r_get_account_id.cut_off,
                                                    c_value_date,
                                                    r_get_account_id.target_balance,
                                                    v_predict_balance,
                                                    ABS(r_get_account_id.target_balance - (v_predict_balance)),
                                                    r_get_account_id.min_sweep_amount,
                                                    r_get_account_id.currency_priority_order,
                                                    r_get_account_id.bv_adjust_basis_forecast,
                                                    r_get_account_id.account_type,
                                                    r_get_account_id.forecast_sod_basis_preferred,
                                                    v_external_balance
                                                   );
                       vErrorLoc := '160';
                       -- update heart beat of the entity process
                       pkg_entity_process.SpUpdHeartbeat(v_EntRecTyp.process_name,
                                                         v_EntRecTyp.entity_id);
                   END LOOP;
                   IF v_process_disabled ='N' THEN
                       vErrorLoc := '170';
                       -- Update the process status as 'NOT RUNNING'
                       pkg_entity_process.SpUpdBeginStatus(v_EntRecTyp.process_name,
                                                           v_EntRecTyp.entity_id,
                                                           'N',
                                                           null);
                       vErrorLoc := '180';
                       -- Update the last run status as 'SUCCESS'
                       pkg_entity_process.SpUpdEndStatus(v_EntRecTyp.process_name,
                                                         v_EntRecTyp.entity_id,
                                                         'S');
                   END IF;
               END IF;
               INSERT INTO s_system_log ( system_seq_no,
                                          host_id,
                                          log_date,
                                          user_id,
                                          ip_address,
                                          process,
                                          action,
                                          update_date,
                                          update_user
                                        )
                                 VALUES ( s_system_log_sequence.NEXTVAL,                              -- SYSTEM_SEQ_NO
                                          vHostID,                                                    -- HOST_ID
                                          global_var.sys_date,                                        -- LOG_DATE
                                          CONST_SYSTEM,                                               -- USER_ID
                                          CONST_DBSERVER,                                             -- IP_ADDRESS
                                          'Sweep: Populate Data for Entity '||v_EntRecTyp.entity_id,  -- PROCESS
                                          'End',                                                      -- ACTION
                                          global_var.sys_date,                                        -- UPDATE_DATE
                                          CONST_SYSTEM                                                -- UPDATE_USER
                                        );
               COMMIT;
           END IF; -- Check the disable status of the process
       END LOOP; -- Entity process cursor
   EXCEPTION
      WHEN OTHERS THEN
         vErrorLoc := '190';
         -- Update the process status as 'NOT RUNNING'
         pkg_entity_process.SpUpdBeginStatus(
            vSweepPopulateData,
            v_entity_id,
            'N',
            null
         );
         vErrorLoc := '200';
         -- Update the last run status as 'FAILURE'
         pkg_entity_process.SpUpdEndStatus(
            vSweepPopulateData,
            v_entity_id,
            'F'
         );
         sp_error_log(
            vHostID,
            CONST_SYSTEM,
            CONST_DBSERVER,
            'PKG_PREDICT_JOBS.SP_POPULATE_SWEEP_DATA ' || ' Location ' || vErrorLoc,
            SQLCODE,
            SQLERRM
         );
   END sp_populate_sweep_data;

   PROCEDURE sp_update_sweep_records IS
      c_value_date            DATE;
      v_predict_balance       p_sweep_monitor.predict_balance%TYPE;
      v_external_balance      p_sweep_monitor.external_balance%TYPE;
      v_current_date          DATE := TRUNC(global_var.sys_date);
      v_sweep_days            p_sweep_monitor.sweep_days%TYPE;
      v_cut_off               p_sweep_monitor.cut_off%TYPE;
      v_adj_basis_external    p_account.bv_adjust_basis_external%TYPE;
      v_future_bal_method     p_account.future_balance_method%TYPE;

      CURSOR cr_update_records(p_ent_id s_entity.entity_id%TYPE)
      IS
      SELECT a.host_id,
             a.entity_id ,
             a.currency_code,
             a.account_id,
             a.account_type,
             a.account_name,
             a.account_class,
             a.main_account_id,
             a.manual_sweep_flag,
             a.sweep_days,
             a.cut_off,
             a.value_date,
             a.target_balance,
             a.predict_balance,
             a.currency_priority_order,
             a.start_balance_option,
             a.forecast_sod_basis_preferred,
             a.external_balance
        FROM p_sweep_monitor a
       WHERE a.entity_id =p_ent_id;
   BEGIN

      -- Start process for each entity
      FOR v_EntRecTyp IN pkg_entity_process.ent_cv(vSweepPopulateData)
      LOOP
         -- Execute the update job only if conditions for populate job, running for the same entity are NOT satisfied
         IF pkg_entity_process.FnIsProcessToRun(v_EntRecTyp.entity_id,
                                                v_EntRecTyp.run_time,
                                                v_EntRecTyp.process_name,
                                                'SECONDARY')
         THEN
            FOR r_get_account_id IN cr_update_records(v_EntRecTyp.entity_id)
            LOOP
                SELECT
                       CASE
                          WHEN use_sub_acc_time_only = 'N' THEN
                            main_cut_off
                          ELSE
                            cut_off
                       END AS cut_off,
                       -- Preserve existing functionality by reversing the sign (and allow for past
                       -- value by not simplying applying the abs function)
                       CASE
                           WHEN use_sub_acc_time_only = 'N'
                           THEN
                              -LEAST(main_sweep_days, sweep_days)
                           ELSE
                              -sweep_days
                       END AS sweep_days
                  INTO v_cut_off,
                       v_sweep_days
                  FROM (SELECT a.*,
                               (SELECT sweep_days
                                  FROM p_account c
                                 WHERE c.host_id = a.host_id
                                   AND c.entity_id = a.entity_id
                                   AND c.account_id = r_get_account_id.main_account_id
                               ) main_sweep_days,
                               (SELECT cut_off
                                  FROM p_account c
                                 WHERE c.host_id = a.host_id
                                   AND c.entity_id = a.entity_id
                                   AND c.account_id = r_get_account_id.main_account_id
                               ) main_cut_off,
                               (SELECT use_sub_acc_timing_only
                                  FROM p_account c
                                 WHERE c.host_id = a.host_id
                                   AND c.entity_id = a.entity_id
                                   AND c.account_id = r_get_account_id.main_account_id
                               ) use_sub_acc_time_only
                          FROM p_account a
                         WHERE host_id = r_get_account_id.host_id
                           AND entity_id = r_get_account_id.entity_id
                           AND currency_code = r_get_account_id.currency_code
                           AND account_id = r_get_account_id.account_id
                           AND account_type = r_get_account_id.account_type
                       );

                c_value_date := sweeping_process.get_sweep_date(
                   r_get_account_id.host_id,
                   r_get_account_id.entity_id,
                   r_get_account_id.main_account_id,
                   r_get_account_id.entity_id,
                   r_get_account_id.account_id,
                   v_current_date,
                   v_sweep_days
                );

                SELECT a.bv_adjust_basis_external,a.future_balance_method
                  INTO v_adj_basis_external,v_future_bal_method
                  FROM p_account a
                 WHERE a.host_id = r_get_account_id.host_id
                   AND a.entity_id = r_get_account_id.entity_id
                   AND a.account_id = r_get_account_id.account_id;

                v_predict_balance := ( fn_get_predict_balance(
                                                              r_get_account_id.host_id,
                                                              r_get_account_id.entity_id,
                                                              r_get_account_id.currency_code,
                                                              r_get_account_id.account_id,
                                                              r_get_account_id.forecast_sod_basis_preferred,
                                                              c_value_date,
                                                              v_current_date
                                                             )
                                      + fn_get_int_sod_future_adj (
                                                                  r_get_account_id.host_id,
                                                                  r_get_account_id.entity_id,
                                                                  r_get_account_id.currency_code,
                                                                  r_get_account_id.account_id,
                                                                  r_get_account_id.account_type,
                                                                  r_get_account_id.start_balance_option,
                                                                  v_future_bal_method,
                                                                  c_value_date,
                                                                  v_current_date
                                                                 )
                                      - CASE
                                          WHEN r_get_account_id.forecast_sod_basis_preferred =  'E'
                                          THEN PKG_PREDICT_JOBS.fn_get_open_unexpected_balance(
                                                                   r_get_account_id.host_id,
                                                                   r_get_account_id.entity_id,
                                                                   r_get_account_id.currency_code,
                                                                   r_get_account_id.account_id,
                                                                   r_get_account_id.account_class,
                                                                   c_value_date,
                                                                   v_current_date
                                                                   )
                                          ELSE 0
                                        END 
                                     );

                v_external_balance := ( fn_get_predict_balance_ext(
                                                                   r_get_account_id.host_id,
                                                                   r_get_account_id.entity_id,
                                                                   r_get_account_id.currency_code,
                                                                   r_get_account_id.account_id,
                                                                   c_value_date
                                                                  )
                                       + fn_get_ext_sod_future_adj(
                                                                   r_get_account_id.host_id,
                                                                   r_get_account_id.entity_id,
                                                                   r_get_account_id.currency_code,
                                                                   r_get_account_id.account_id,
                                                                   r_get_account_id.account_type,
                                                                   v_adj_basis_external,
                                                                   v_future_bal_method,
                                                                   c_value_date,
                                                                   v_current_date
                                                                  )
                                      );

                UPDATE p_sweep_monitor
                   SET value_date       = c_value_date,
                       predict_balance  = v_predict_balance,
                       external_balance = v_external_balance,
                       cut_off          = v_cut_off,
                       sweep_days       = v_sweep_days,
                       sweep_balance    = ABS(target_balance - (v_predict_balance))
                 WHERE host_id          = r_get_account_id.host_id
                   AND entity_id        = r_get_account_id.entity_id
                   AND currency_code    = r_get_account_id.currency_code
                   AND account_id       = r_get_account_id.account_id
                   AND NOT (    value_date       = c_value_date
                            AND predict_balance  = v_predict_balance
                            AND external_balance = v_external_balance
                            AND cut_off          = v_cut_off
                            AND sweep_days       = v_sweep_days
                            AND sweep_balance    = ABS(target_balance - (v_predict_balance))
                           );
            END LOOP;
         END IF;
      END LOOP;
      COMMIT;
   EXCEPTION
      WHEN OTHERS THEN
         SP_ERROR_LOG(
            vHostID,
            CONST_SYSTEM,
            CONST_DBSERVER,
            'PKG_PREDICT_JOBS.SP_UPDATE_SWEEP_RECORDS',
            SQLCODE,
            SQLERRM
         );
   END sp_update_sweep_records;

   PROCEDURE sp_populate_group_data IS
      v_source            VARCHAR(200);
      -- use when row lock exception raised
      ROW_LOCKED          EXCEPTION;
      PRAGMA              EXCEPTION_INIT(ROW_LOCKED, -54);

      -- use when error raise in forall
      vForAllExcep        EXCEPTION;
      PRAGMA              EXCEPTION_INIT(vForAllExcep, -24381);

      -- use to capture other than dup_val_index exception
      vOtherExceptions    EXCEPTION;
      PRAGMA              EXCEPTION_INIT(vOtherExceptions, -20001);

      v_db_sid            s_entity_process_status.database_session%TYPE;
      v_entity_id         s_entity.entity_id%TYPE;
      v_process_disabled  VARCHAR2(1) := 'N';
      vErrorLoc           VARCHAR2(10);
      v_current_date      DATE := TRUNC(GLOBAL_VAR.SYS_DATE);
      v_start_date        DATE := v_current_date - 1;
      v_end_date          DATE := v_current_date + Get_Days_After_Today_Books();
      v_error_count       PLS_INTEGER;
      v_sql_code          VARCHAR2(15);
      v_limit             PLS_INTEGER := 1000; -- bulk fetch limit

      CURSOR cr_group_data(p_host_id VARCHAR2, p_entity_id VARCHAR2) IS
      SELECT a.host_id,
             a.entity_id,
             a.currency_code,
             a.bookcode,
             NVL(b.group_id, 'OTHERS') group_id,
             a.bal_date balance_date,
             NVL(b.location_id, 'OTHERS') location_id,
             NVL(b.metagroup_id, 'OTHERS') meta_group,
             a.total_balance,
             'N' process_flag
        FROM ( SELECT n.host_id, n.entity_id, n.bal_date, n.currency_Code, n.bookcode,
                      NVL(m.total_balance,0) total_balance
                 FROM ( SELECT m.host_id, m.entity_id, m.value_date, m.currency_Code,
                               NVL(m.bookcode,'OTHERS') bookcode,
                               SUM(CASE m.sign
                                   WHEN 'C' THEN
                                     m.amount
                                   WHEN 'D' THEN
                                     -m.amount
                               END ) total_balance
                          FROM P_Movement m
                         WHERE m.host_id = p_host_id
                           AND m.entity_id = p_entity_id
                           AND m.value_date BETWEEN v_current_date AND v_end_date
                           AND m.match_status NOT IN ('A','R')
                           AND m.predict_status = 'I'
                         GROUP BY m.host_id, m.entity_id, m.value_date, m.currency_code, NVL(m.bookcode,'OTHERS')
                      ) m,
                      ( SELECT DISTINCT m.host_id, m.entity_id, m.currency_code,
                               NVL(m.bookcode,'OTHERS') bookcode, bal_date
                          FROM p_movement m, (SELECT v_start_date + LEVEL AS bal_date
                                                FROM DUAL
                                             CONNECT BY LEVEL <= (Get_Days_After_Today_Books() + 1)
                                             ) dt
                         WHERE m.host_id = p_host_id
                           AND m.entity_id = p_entity_id
                      ) n
                WHERE m.host_id(+) = n.host_id
                  AND m.entity_id(+) = n.entity_id
                  AND m.currency_Code(+) = n.currency_code
                  AND m.value_date(+) = n.bal_date
                  AND m.bookcode(+) = n.bookcode
             ) a,
             ( SELECT t.host_id,
                      t.entity_id,
                      t.bookcode,
                      t.group_id,
                      NVL(t.location_id,'OTHERS') location_id,
                      NVL(g.metagroup_id,'OTHERS') metagroup_id
                 FROM (SELECT b.host_id,
                              b.entity_id,
                              b.bookcode,
                              CASE WHEN (    b.group_id_level_1 IS NUll
                                         AND b.group_id_level_2 IS NUll
                                         AND b.group_id_level_3 IS NUll) THEN
                                       'OTHERS'
                              ELSE
                                   CASE WHEN group_table.rowno = 1 THEN
                                           b.group_id_level_1
                                        WHEN group_table.rowno = 2 THEN
                                           b.group_id_level_2
                                        WHEN group_table.rowno = 3 THEN
                                           b.group_id_level_3
                                   END
                              END group_id,
                              NVL(b.location_id,'OTHERS') location_id
                         FROM p_bookcode b, (SELECT ROWNUM rowno
                                               FROM DUAL
                                            CONNECT BY LEVEL <= 3
                                            ) group_table
                        WHERE b.host_id = p_host_id
                          AND b.entity_id = p_entity_id
                        ORDER BY 1,2,3,4
                      ) t, p_group g
                WHERE t.host_id = g.host_id(+)
                  AND t.entity_id = g.entity_id(+)
                  AND t.group_id = g.group_id(+)
                  AND t.group_id IS NOT NULL
                GROUP BY t.host_id, t.entity_id, t.bookcode, t.group_id, t.location_id, metagroup_id
             ) b
       WHERE a.host_id = b.host_id(+)
         AND a.entity_id = b.entity_id(+)
         AND a.bookcode = b.bookcode(+);

      TYPE fetch_cur      IS TABLE OF cr_group_data%ROWTYPE;
      v_fet_cur           fetch_cur;
      vIndex              PLS_INTEGER;
   BEGIN
      vErrorLoc := '10';
      -- Start process for each entity
      FOR v_EntRecTyp IN pkg_entity_process.ent_cv(vGroupPopulateData)
      LOOP
         v_entity_id := v_EntRecTyp.entity_id;
         v_process_disabled := 'N';
         vErrorLoc := '20';
         -- get the status for the supplied entity (Determine if process is to execute for this entity)
         IF pkg_entity_process.FnIsProcessToRun(
            v_EntRecTyp.entity_id,
            v_EntRecTyp.run_time,
            v_EntRecTyp.process_name,
            'PRIMARY'
         )
         THEN
            vErrorLoc := '30';
            INSERT INTO s_system_log ( system_seq_no,
                                       host_id,
                                       log_date,
                                       user_id,
                                       ip_address,
                                       process,
                                       action,
                                       update_date,
                                       update_user
                                      )
                               VALUES ( s_system_log_sequence.NEXTVAL,                               -- SYSTEM_SEQ_NO
                                        vHostID,                                                     -- HOST_ID
                                        global_var.sys_date,                                         -- LOG_DATE
                                        CONST_SYSTEM,                                                -- USER_ID
                                        CONST_DBSERVER,                                              -- IP_ADDRESS
                                        'Group: Populate Data for Entity '||v_EntRecTyp.entity_id,   -- PROCESS
                                        'Start',                                                     -- ACTION
                                        global_var.sys_date,                                         -- UPDATE_DATE
                                        CONST_SYSTEM                                                 -- UPDATE_USER
                                      );
            COMMIT;
            vErrorLoc := '40';
            -- Get Oracle SID (session id) for the session doing the work.
            v_db_sid := SYS_CONTEXT('USERENV','SID');
            vErrorLoc := '50';
            -- Update the process status as 'RUNNING'
            pkg_entity_process.SpUpdBeginStatus(
               v_EntRecTyp.process_name,
               v_EntRecTyp.entity_id,
               'R',
               v_db_sid
            );
            vErrorLoc := '60';
            -- update heart beat of the entity process
            pkg_entity_process.SpUpdHeartbeat(
               v_EntRecTyp.process_name,
               v_EntRecTyp.entity_id
            );

            vErrorLoc := '70';
            -- Check the disable status of the process, if diable is requested then
            -- update the status as diabled for this entity and process next
            IF pkg_entity_process.FnIsProcessToDisable(
               v_EntRecTyp.process_name,
               v_EntRecTyp.entity_id
            ) THEN
               vErrorLoc := '80';
               -- Update the process status as 'DISABLE'
               pkg_entity_process.SpUpdBeginStatus(
                  v_EntRecTyp.process_name,
                  v_EntRecTyp.entity_id,
                  'D',
                  null
               );
               vErrorLoc := '90';
               -- Update the last run status as 'DISABLE'
               pkg_entity_process.SpUpdEndStatus(
                  v_EntRecTyp.process_name,
                  v_EntRecTyp.entity_id,
                  'D'
               );
            ELSE
               vErrorLoc := '100';
               DELETE FROM p_group_monitor
                WHERE entity_id = v_EntRecTyp.entity_id;
               COMMIT;
               vErrorLoc := '110';

               OPEN cr_group_data(v_EntRecTyp.host_id, v_entity_id);
               LOOP
                  BEGIN
                     BEGIN

                        vErrorLoc := '120';
                        -- Fetching cursor records into v_fet_cur
                        FETCH cr_group_data BULK COLLECT INTO v_fet_cur LIMIT v_limit;

                        vErrorLoc := '130';

                        IF pkg_entity_process.FnIsProcessToDisable(
                           v_EntRecTyp.process_name,
                           v_EntRecTyp.entity_id
                        ) THEN
                           -- Update the process status as 'DISABLE'
                           vErrorLoc := '140';
                           pkg_entity_process.SpUpdBeginStatus(
                              v_EntRecTyp.process_name,
                              v_EntRecTyp.entity_id,
                              'D',
                              null
                           );
                           vErrorLoc := '150';
                           -- Update the last run status as 'DISABLE'
                           pkg_entity_process.SpUpdEndStatus(
                              v_EntRecTyp.process_name,
                              v_EntRecTyp.entity_id,
                              'D'
                           );
                           v_process_disabled := 'Y';
                           EXIT;
                        END IF;
                        vErrorLoc := '160';

                        -- Save an exception raised during forall DML operation
                        FORALL j IN 1..v_fet_cur.COUNT SAVE EXCEPTIONS
                               INSERT INTO p_group_monitor VALUES v_fet_cur(j);

                        -- Exit when no records found in the cursor
                        EXIT WHEN cr_group_data%NOTFOUND;
                     EXCEPTION
                        WHEN row_locked THEN
                           NULL;
                        -- Handle exceptions thrown in the FORALL loop
                        WHEN vforallexcep THEN

                           -- Taking count of saved exception
                           v_error_count := SQL%BULK_EXCEPTIONS.COUNT;
                           vErrorLoc := '170';

                           FOR J IN 1..v_error_count
                           LOOP
                              -- Gettting error code of saved exceptions
                               v_sql_code :=  SUBSTR(SQLERRM(-SQL%BULK_EXCEPTIONS(j).ERROR_CODE),1,9);
                               vErrorLoc := '180';

                              -- Checking error code is 'ORA-00001' for DUP_VAL_ON_INDEX exception
                               IF v_sql_code = 'ORA-00001'
                               THEN
                                 vIndex := SQL%BULK_EXCEPTIONS(j).ERROR_INDEX;
                                 vErrorLoc := '190';

                                 -- Performing update for exception raised records
                                 UPDATE p_group_monitor
                                    SET total_balance = v_fet_cur(vIndex).total_balance
                                  WHERE host_id       = v_fet_cur(vIndex).host_id
                                    AND entity_id     = v_fet_cur(vIndex).entity_id
                                    AND currency_code = v_fet_cur(vIndex).currency_code
                                    AND bookcode      = v_fet_cur(vIndex).bookcode
                                    AND group_id      = v_fet_cur(vIndex).group_id
                                    AND balance_date  = v_fet_cur(vIndex).balance_date
                                    AND location_id   = v_fet_cur(vIndex).location_id
                                    AND meta_group    = v_fet_cur(vIndex).meta_group;

                                 vErrorLoc := '200';
                               ELSE
                                 -- other than dup val exception allow to log into error log table
                                 RAISE vOtherExceptions;
                               END IF;
                           END LOOP;
                     END;
                  EXCEPTION
                     WHEN vOtherExceptions THEN
                        sp_error_log(
                           vHostID,
                           CONST_SYSTEM,
                           CONST_DBSERVER,
                           'PKG_PREDICT_JOBS.SP_POPULATE_GROUP_DATA ' || v_source || ' Location ' || vErrorLoc,
                           SQLCODE,
                           SQLERRM
                        );

                        -- Update the process status as 'NOT RUNNING'
                        vErrorLoc := '210';
                        pkg_entity_process.SpUpdBeginStatus(
                           v_EntRecTyp.process_name,
                           v_EntRecTyp.entity_id,
                           'N',
                           null
                        );
                        vErrorLoc := '220';
                        -- Update the last run status as 'FAILURE'
                        pkg_entity_process.SpUpdEndStatus(
                           v_EntRecTyp.process_name,
                           v_EntRecTyp.entity_id,
                           'F'
                        );
                  END;
               END LOOP;
               COMMIT;
               CLOSE cr_group_data;

               vErrorLoc := '230';
               -- update heart beat of the entity process
               pkg_entity_process.SpUpdHeartbeat(
                  v_EntRecTyp.process_name,
                  v_EntRecTyp.entity_id
               );
               -- update heart beat of the entity process
               pkg_entity_process.SpUpdHeartbeat(
                  v_EntRecTyp.process_name,
                  v_EntRecTyp.entity_id
               );

                IF v_process_disabled = 'N' THEN
                  vErrorLoc := '240';
                  -- Update the process status as 'NOT RUNNING'
                  pkg_entity_process.SpUpdBeginStatus(
                     v_EntRecTyp.process_name,
                     v_EntRecTyp.entity_id,
                     'N',
                     null
                  );
                  vErrorLoc := '250';
                  -- Update the last run status as 'SUCCESS'
                  pkg_entity_process.SpUpdEndStatus(
                     v_EntRecTyp.process_name,
                     v_EntRecTyp.entity_id,
                     'S'
                  );
                END IF;
            END IF;

            INSERT INTO s_system_log (system_seq_no,
                                      host_id,
                                      log_date,
                                      user_id,
                                      ip_address,
                                      process,
                                      action,
                                      update_date,
                                      update_user
                                      )
                               VALUES (s_system_log_sequence.NEXTVAL,                             -- SYSTEM_SEQ_NO
                                      vHostID,                                                    -- HOST_ID
                                      global_var.sys_date,                                        -- LOG_DATE
                                      CONST_SYSTEM,                                               -- USER_ID
                                      CONST_DBSERVER,                                             -- IP_ADDRESS
                                      'Group: Populate Data for Entity '||v_EntRecTyp.entity_id,  -- PROCESS
                                      'End',                                                      -- ACTION
                                      global_var.sys_date,                                        -- UPDATE_DATE
                                     CONST_SYSTEM                                                 -- UPDATE_USER
                                     );
            COMMIT;
         END IF;  -- Check the disable status of the process
      END LOOP; -- Entity process cursor
      vErrorLoc := '260';
   EXCEPTION
      WHEN OTHERS THEN
         -- Update the process status as 'NOT RUNNING'
         pkg_entity_process.SpUpdBeginStatus(
            vGroupPopulateData,
            v_entity_id,
            'N',
            null
         );
         -- Update the last run status as 'FAILURE'
         pkg_entity_process.SpUpdEndStatus(
            vGroupPopulateData,
            v_entity_id,
            'F'
         );

         sp_error_log(
            vHostID,
            CONST_SYSTEM,
            CONST_DBSERVER,
            'PKG_PREDICT_JOBS.SP_POPULATE_GROUP_DATA ' || 'Location ' || vErrorLoc,
            SQLCODE,
            SQLERRM
         );
   END sp_populate_group_data;

   PROCEDURE sp_populate_additional_gr_data(
      p_host_id       IN  VARCHAR2,
      p_entity_id     IN  VARCHAR2,
      p_currency_code IN  VARCHAR2,
      p_balance_date  IN  DATE
   )
   IS
      v_count            NUMBER;
      v_date1            DATE;
      v_date2            DATE;

      CURSOR cr_group_data(
         p_host_id         IN   VARCHAR2,
         p_entity_id       IN   VARCHAR2,
         p_currency_code   IN   VARCHAR2,
         p_balance_date    IN   VARCHAR2
      ) IS
      SELECT a.host_id,
             a.entity_id,
             a.currency_Code,
             NVL(b.group_id,'OTHERS') group_id,
             a.bookcode,
             a.value_date,
             NVL(b.location_id,'OTHERS') location_id,
             NVL(b.metagroup_id,'OTHERS') meta_group,
             a.total_balance
        FROM ( SELECT n.host_id, n.entity_id, n.value_date, n.currency_Code, n.bookcode,
                      NVL(m.total_balance,0) total_balance
                 FROM ( SELECT m.host_id, m.entity_id, m.value_date, m.currency_Code,
                               NVL(m.bookcode,'OTHERS') bookcode,
                               SUM(CASE M.SIGN
                                   WHEN 'C' THEN
                                     M.AMOUNT
                                   WHEN 'D' THEN
                                     -M.AMOUNT
                               END ) total_balance
                          FROM P_Movement m
                         WHERE m.host_id = p_host_id
                           AND m.entity_id = p_entity_id
                           AND m.currency_code = p_currency_code
                           AND m.value_date = p_balance_date
                           AND m.match_status NOT IN ('A','R')
                           AND m.predict_status = 'I'
                         GROUP BY m.host_id, m.entity_id, m.value_date, m.currency_code, NVL(m.bookcode,'OTHERS')
                      ) m,
                      ( SELECT DISTINCT m.host_id, m.entity_id, m.currency_code,
                               NVL(m.bookcode,'OTHERS') bookcode, value_date
                          FROM p_movement m
                         WHERE m.host_id = p_host_id
                           AND m.entity_id = p_entity_id
                           AND m.currency_code = p_currency_code
                           AND m.value_date = p_balance_date
                      ) n
                WHERE m.host_id(+) = n.host_id
                  AND m.entity_id(+) = n.entity_id
                  AND m.currency_Code(+) = n.currency_code
                  AND m.value_date(+) = n.value_date
                  AND m.bookcode(+) = n.bookcode
             ) a,
             ( SELECT t.host_id,
                      t.entity_id,
                      t.bookcode,
                      t.group_id,
                      NVL(t.location_id,'OTHERS') location_id,
                      NVL(g.metagroup_id,'OTHERS') metagroup_id
                 FROM (SELECT b.host_id,
                              b.entity_id,
                              b.bookcode,
                              CASE WHEN (    b.group_id_level_1 IS NUll
                                         AND b.group_id_level_2 IS NUll
                                         AND b.group_id_level_3 IS NUll) THEN
                                       'OTHERS'
                              ELSE
                                   CASE WHEN group_table.rowno = 1 THEN
                                           b.group_id_level_1
                                        WHEN group_table.rowno = 2 THEN
                                           b.group_id_level_2
                                        WHEN group_table.rowno = 3 THEN
                                           b.group_id_level_3
                                   END
                              END group_id,
                              NVL(b.location_id,'OTHERS') location_id
                         FROM p_bookcode b, (SELECT ROWNUM rowno
                                               FROM DUAL
                                            CONNECT BY LEVEL <= 3
                                            ) group_table
                        WHERE b.host_id = p_host_id
                          AND b.entity_id = p_entity_id
                        ORDER BY 1,2,3,4
                      ) t, p_group g
                WHERE t.host_id = g.host_id(+)
                  AND t.entity_id = g.entity_id(+)
                  AND t.group_id = g.group_id(+)
                  AND t.group_id IS NOT NULL
                GROUP BY t.host_id, t.entity_id, t.bookcode, t.group_id, t.location_id, metagroup_id
             ) b
       WHERE a.host_id = b.host_id(+)
         AND a.entity_id = b.entity_id(+)
         AND a.bookcode = b.bookcode(+);
   BEGIN
      v_date1 := TRUNC (global_var.sys_date);
      v_date2 := v_date1 + Get_Days_After_Today_Books();

      IF p_balance_date BETWEEN v_date1 AND v_date2
      THEN
         SELECT COUNT (1)
           INTO v_count
           FROM p_group_monitor
          WHERE host_id = p_host_id
            AND entity_id = p_entity_id
            AND currency_code = p_currency_code
            AND balance_date = p_balance_date;
      ELSE
         DELETE FROM p_group_monitor
               WHERE host_id = p_host_id
                 AND entity_id = p_entity_id
                 AND currency_code = p_currency_code
                 AND balance_date = p_balance_date;

         COMMIT;
         v_count := 0;
      END IF;

      IF v_count = 0
      THEN
         FOR group_data IN cr_group_data(
            p_host_id,
            p_entity_id,
            p_currency_code,
            p_balance_date
         )
         LOOP
            BEGIN
               INSERT INTO p_group_monitor
                           (
                            host_id,
                            entity_id,
                            currency_code,
                            GROUP_ID,
                            bookcode,
                            balance_date,
                            location_id,
                            meta_group,
                            total_balance,
                            process_flag
                           )
                    VALUES (
                            group_data.host_id,
                            group_data.entity_id,
                            group_data.currency_code,
                            group_data.group_id,
                            group_data.bookcode,
                            group_data.value_date,
                            group_data.location_id,
                            group_data.meta_group,
                            group_data.total_balance,
                            'N'
                           );
            END;
         END LOOP;
         COMMIT;
      END IF;
   EXCEPTION
      WHEN OTHERS
      THEN
         sp_error_log(
            vHostID,
            CONST_SYSTEM,
            CONST_DBSERVER,
            'PKG_PREDICT_JOBS.SP_POPULATE_ADDITIONAL_GR_DATA',
            SQLCODE,
            SQLERRM
         );
   END sp_populate_additional_gr_data;

   PROCEDURE sp_update_group_data
   IS
      v_host_id            s_host.host_id%TYPE        := vHostID;
      v_entity_id          s_entity.entity_id%TYPE;
      vErrorLoc            VARCHAR2(10);
      v_date1              DATE;
      v_date2              DATE;
      v_limit              PLS_INTEGER                := 1000; -- bulk fetch limit

      -- use when row lock exception raised
      ROW_LOCKED          EXCEPTION;
      PRAGMA              EXCEPTION_INIT(ROW_LOCKED, -54);

      -- use when error raise in forall
      vForAllExcep        EXCEPTION;
      PRAGMA              EXCEPTION_INIT(vForAllExcep, -24381);

      TYPE tCurrencyCode IS TABLE OF p_group_monitor.currency_code%TYPE
      INDEX BY BINARY_INTEGER;

      TYPE tBookcode IS TABLE OF p_group_monitor.bookcode%TYPE
      INDEX BY BINARY_INTEGER;

      TYPE tGroupId IS TABLE OF p_group_monitor.Group_id%TYPE
      INDEX BY BINARY_INTEGER;

      TYPE tBalanceDate IS TABLE OF p_group_monitor.balance_date%TYPE
      INDEX BY BINARY_INTEGER;

      TYPE tTotalBalance IS TABLE OF p_group_monitor.total_balance%TYPE
      INDEX BY BINARY_INTEGER;

      vCurrencyCode        tCurrencyCode;
      vBookcode            tBookcode;
      vGroupId             tGroupId;
      vBalanceDate         tBalanceDate;
      vTotalBalance        tTotalBalance;
      vErrorCount          PLS_INTEGER;
      vIndex               PLS_INTEGER;

      CURSOR cr_group_data(p_entity_id VARCHAR2) IS
      SELECT currency_code, bookcode, GROUP_ID, balance_date,
             ( SELECT  SUM (DECODE (m.SIGN, 'C', m.amount, -m.amount))
                  FROM p_movement m
                 WHERE g.host_id = m.host_id
                   AND g.entity_id = m.entity_id
                   AND g.currency_code = m.currency_code
                   AND g.bookcode = NVL (m.bookcode, 'OTHERS')
                   AND g.process_flag = 'Y'
                   AND g.balance_date = m.value_date
                   AND m.match_status NOT IN ('A', 'R')
                   AND m.predict_status = 'I'
                   AND m.value_date BETWEEN v_date1 AND v_date2
                   AND m.host_id = v_host_id
                   AND m.entity_id = p_entity_id
              GROUP BY g.host_id,
                       g.entity_id,
                       g.currency_code,
                       g.bookcode,
                       g.GROUP_ID,
                       g.balance_date,
                       g.location_id,
                       g.meta_group) total_balance
        FROM p_group_monitor g
       WHERE g.host_id = v_host_id
         AND g.entity_id = p_entity_id
         AND g.process_flag = 'Y'
         AND g.balance_date BETWEEN v_date1 AND v_date2;
   BEGIN
      vErrorLoc := '10';
      v_date1 := TRUNC(global_var.sys_date);
      v_date2 := v_date1 + Get_Days_After_Today_Books();

      vErrorLoc := '20';
      -- Start process for each entity
      FOR v_EntRecTyp IN pkg_entity_process.ent_cv(vGroupPopulateData)
      LOOP
         vErrorLoc := '30';
         -- Execute the update job only if conditions for populate job, running for the same entity are NOT satisfied
         IF pkg_entity_process.FnIsProcessToRun(
            v_EntRecTyp.entity_id,
            v_EntRecTyp.run_time,
            v_EntRecTyp.process_name,
            'SECONDARY'
         )
         THEN
            v_entity_id := v_EntRecTyp.entity_id;

            vErrorLoc := '40';
            OPEN cr_group_data(v_entity_id);
            LOOP
               BEGIN
                  BEGIN
                     vErrorLoc := '50';
                     FETCH cr_group_data BULK COLLECT INTO
                        vCurrencyCode,
                        vBookcode,
                        vGroupId,
                        vBalanceDate,
                        vTotalBalance
                     LIMIT v_limit;

                     vErrorLoc := '60';
                     -- Save an exception raised during forall DML operation
                     FORALL j IN vCurrencyCode.FIRST..vCurrencyCode.LAST SAVE EXCEPTIONS
                        UPDATE p_group_monitor
                           SET total_balance = vTotalBalance(j),
                               process_flag  = 'N'
                         WHERE host_id       = v_host_id
                           AND entity_id     = v_entity_id
                           AND group_id      = vGroupId(j)
                           AND currency_code = vCurrencyCode(j)
                           AND bookcode      = vBookcode(j)
                           AND balance_date  = vBalanceDate(j);
                     COMMIT;
                     vErrorLoc := '70';
                     -- Exit when no records found in the cursor
                     EXIT WHEN cr_group_data%NOTFOUND;
                  EXCEPTION
                     WHEN row_locked THEN
                        vErrorLoc := '80';
                        NULL;
                     -- Handle exceptions thrown in the FORALL loop
                     WHEN vforallexcep THEN

                        vErrorLoc := '90';
                        -- Taking count of saved exception
                        vErrorCount := SQL%BULK_EXCEPTIONS.COUNT;

                        FOR J IN 1..vErrorCount
                        LOOP
                           vErrorLoc := '100';
                           vIndex := SQL%BULK_EXCEPTIONS(j).ERROR_INDEX;

                           vErrorLoc := '110';
                           -- Performing update for exception raised records
                           UPDATE p_group_monitor
                              SET total_balance = vTotalBalance(vIndex),
                                  process_flag  = 'N'
                            WHERE host_id       = v_host_id
                              AND entity_id     = v_entity_id
                              AND group_id      = vGroupId(vIndex)
                              AND currency_code = vCurrencyCode(vIndex)
                              AND bookcode      = vBookcode(vIndex)
                              AND balance_date  = vBalanceDate(vIndex);
                           COMMIT;
                        END LOOP;
                  END;
               EXCEPTION
                  WHEN OTHERS THEN
                     sp_error_log(
                        v_host_id,
                        CONST_SYSTEM,
                        CONST_DBSERVER,
                        'PK_MONITOR.SP_UPDATE_GROUP_DATA ' || 'Location ' || vErrorLoc,
                        SQLCODE,
                        SQLERRM
                     );
               END;
            END LOOP;
            CLOSE cr_group_data;

            vErrorLoc := '130';
            UPDATE p_group_monitor set total_balance = 0 where total_balance IS NULL;
            COMMIT;
         END IF;
      END LOOP;
   EXCEPTION
      WHEN OTHERS THEN
         sp_error_log(
            v_host_id,
            CONST_SYSTEM,
            CONST_DBSERVER,
            'PK_MONITOR.SP_UPDATE_GROUP_DATA ' || 'Location ' || vErrorLoc,
            SQLCODE,
            SQLERRM
         );
   END sp_update_group_data;

   PROCEDURE sp_update_workflow_job
   IS
       v_system_date      DATE;
   BEGIN
       -- Start process for each entity
       FOR v_EntRecTyp IN pkg_entity_process.ent_cv(vMonitorPopulateData)
       LOOP
           -- Execute the update job only if conditions for populate job, running for the same entity are NOT satisfied
           IF pkg_entity_process.FnIsProcessToRun(v_EntRecTyp.entity_id,
                                                  v_EntRecTyp.run_time,
                                                  v_EntRecTyp.process_name,
                                                  'SECONDARY')
           THEN
               v_system_date:= TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(GLOBAL_VAR.SYS_DATE,v_EntRecTyp.entity_id));
               DECLARE
                   CURSOR cr_internal_movements
                       IS SELECT COUNT (wot_mvmt_id) wot_count,
                                 COUNT (wt_mvmt_id) wt_count,
                                 host_id,
                                 entity_id,
                                 currency_group_id,
                                 position_level
                            FROM (SELECT m.movement_id wot_mvmt_id,
                                         CASE
                                         WHEN m.amount >= s.threshold_product THEN
                                             m.movement_id
                                         ELSE
                                             NULL
                                         END wt_mvmt_id,
                                         s.host_id,
                                         s.entity_id,
                                         s.currency_group_id,
                                         m.position_level
                                    FROM p_movement m, s_currency s
                                   WHERE m.predict_status = 'I'
                                     AND m.match_status != 'E'
                                     AND s.entity_id = v_EntRecTyp.entity_id
                                     AND m.value_date = v_system_date
                                     AND s.host_id = m.host_id
                                     AND s.entity_id = m.entity_id
                                     AND s.currency_code = m.currency_code
                                   UNION ALL
                                  SELECT m.movement_id wot_mvmt_id,
                                         CASE
                                         WHEN m.amount >= s.threshold_product THEN
                                             m.movement_id
                                         ELSE
                                             NULL
                                         END wt_mvmt_id,
                                         s.host_id,
                                         s.entity_id,
                                         s.currency_group_id,
                                         m.position_level
                                    FROM p_movement m, s_currency s
                                   WHERE m.predict_status = 'I'
                                     AND m.match_status != 'E'
                                     AND m.open = 'Y'
                                     AND s.entity_id = v_EntRecTyp.entity_id
                                     AND m.value_date < v_system_date
                                     AND s.host_id = m.host_id
                                     AND s.entity_id = m.entity_id
                                     AND s.currency_code = m.currency_code
                                 )
                           GROUP BY host_id, entity_id, currency_group_id, position_level;
               BEGIN
                   UPDATE p_workflow_chart_temp
                      SET t_included_movements_count = 0,
                          included_movements_count = 0
                    WHERE host_id = vHostID
                      AND entity_id = v_EntRecTyp.entity_id
                      AND VALUE_DATE = v_system_date;

                   COMMIT;

                   FOR r_data IN cr_internal_movements
                   LOOP
                       UPDATE p_workflow_chart_temp
                          SET included_movements_count = r_data.wot_count,
                              t_included_movements_count = r_data.wt_count
                        WHERE host_id = r_data.host_id
                          AND entity_id = r_data.entity_id
                          AND currency_group_id = r_data.currency_group_id
                          AND position_level = r_data.position_level
                          AND VALUE_DATE = v_system_date;
                   END LOOP;

                   COMMIT;
               END;

               DECLARE

                   CURSOR cr_excluded_movements
                       IS SELECT COUNT (wot_mvmt_id) wot_count,
                                 COUNT (wt_mvmt_id) wt_count,
                                 host_id,
                                 entity_id,
                                 currency_group_id,
                                 position_level
                            FROM (SELECT a.movement_id wot_mvmt_id,
                                         CASE
                                         WHEN a.amount >= s.threshold_product THEN
                                             a.movement_id
                                         ELSE
                                             NULL
                                         END wt_mvmt_id,
                                         s.host_id,
                                         s.entity_id,
                                         s.currency_group_id,
                                         a.position_level
                                    FROM p_movement a, s_currency s
                                   WHERE a.match_status = 'L'
                                     AND a.predict_status = 'E'
                                     AND a.value_date = v_system_date
                                     AND s.entity_id = v_EntRecTyp.entity_id
                                     AND 'L' <> (SELECT account_class
                                                   FROM p_account c
                                                  WHERE c.host_id = a.host_id
                                                    AND c.entity_id = a.entity_id
                                                    AND c.entity_id = v_EntRecTyp.entity_id
                                                    AND c.account_id = a.account_id)
                                     AND a.amount >= fn_exchange_rate(
                                                       s.host_id,
                                                       s.entity_id,
                                                       s.currency_code,
                                                       a.movement_type
                                                     )
                                    AND s.host_id = a.host_id
                                    AND s.entity_id = a.entity_id
                                    AND s.currency_code = a.currency_code
                                  UNION ALL
                                 SELECT a.movement_id wot_mvmt_id,
                                        CASE
                                        WHEN a.amount >= s.threshold_product THEN
                                            a.movement_id
                                        ELSE
                                            NULL
                                        END wt_mvmt_id,
                                        s.host_id,
                                        s.entity_id,
                                        s.currency_group_id,
                                        a.position_level
                                   FROM p_movement a, s_currency s
                                  WHERE a.match_status = 'L'
                                    AND a.predict_status = 'E'
                                    AND a.open = 'Y'
                                    AND a.value_date < v_system_date
                                    AND s.entity_id = v_EntRecTyp.entity_id
                                    AND 'L' <> (SELECT account_class
                                                  FROM p_account c
                                                 WHERE c.host_id = a.host_id
                                                   AND c.entity_id = a.entity_id
                                                   AND c.entity_id = v_EntRecTyp.entity_id
                                                   AND c.account_id = a.account_id)
                                    AND a.amount >= fn_exchange_rate(
                                                      s.host_id,
                                                      s.entity_id,
                                                      s.currency_code,
                                                      a.movement_type
                                                    )
                                    AND s.host_id = a.host_id
                                    AND s.entity_id = a.entity_id
                                    AND s.currency_code = a.currency_code
                                 )
                           GROUP BY host_id, entity_id, currency_group_id, position_level;

                   CURSOR cr_exchange_rate
                       IS SELECT s.host_id,
                                 s.entity_id,
                                 s.currency_code,
                            CASE e.exchange_rate_format
                                 WHEN '1'
                                 THEN (  e.cash_filter
                                        * pk_application.fn_get_currency_exchange_rate (s.host_id,
                                                                                        s.entity_id,
                                                                                        s.currency_code
                                                                                       )
                                      )
                                 ELSE (  e.cash_filter
                                       / pk_application.fn_get_currency_exchange_rate (s.host_id,
                                                                                       s.entity_id,
                                                                                       s.currency_code
                                                                                      )
                                      )
                            END cash_filter,
                            CASE e.exchange_rate_format
                                 WHEN '1'
                                 THEN (  e.securities_filter
                                       * pk_application.fn_get_currency_exchange_rate (s.host_id,
                                                                                       s.entity_id,
                                                                                       s.currency_code
                                                                                      )
                                      )
                                 ELSE (  e.securities_filter
                                       / pk_application.fn_get_currency_exchange_rate (s.host_id,
                                                                                       s.entity_id,
                                                                                       s.currency_code
                                                                                      )
                                      )
                            END securities_filter
                       FROM s_currency s, s_entity e
                      WHERE e.host_id = s.host_id
                        AND e.entity_id = s.entity_id
                        AND e.entity_id = v_EntRecTyp.entity_id;
               BEGIN
                   FOR r_data IN cr_exchange_rate
                   LOOP
                       ex_mat (r_data.host_id) (r_data.entity_id) (r_data.currency_code).cash_filter :=
                          r_data.cash_filter;
                       ex_mat (r_data.host_id) (r_data.entity_id) (r_data.currency_code).securities_filter :=
                          r_data.securities_filter;
                   END LOOP;

                   UPDATE p_workflow_chart_temp
                      SET t_excluded_movements_count = 0,
                          excluded_movements_count = 0
                    WHERE host_id = vHostID
                      AND entity_id = v_EntRecTyp.entity_id
                      AND VALUE_DATE = v_system_date;


                   FOR r_data IN cr_excluded_movements
                   LOOP
                       UPDATE p_workflow_chart_temp
                          SET excluded_movements_count = r_data.wot_count,
                              t_excluded_movements_count = r_data.wt_count
                        WHERE host_id = r_data.host_id
                          AND entity_id = r_data.entity_id
                          AND currency_group_id = r_data.currency_group_id
                          AND position_level = r_data.position_level
                          AND VALUE_DATE = v_system_date;
                   END LOOP;

                COMMIT;
               END;
           END IF;
       END LOOP;
   EXCEPTION
      WHEN OTHERS THEN
         sp_error_log(
            vHostID,
            CONST_SYSTEM,
            CONST_DBSERVER,
            'PKG_PREDICT_JOBS.SP_UPDATE_WORKFLOW_JOB',
            SQLCODE,
            SQLERRM
         );
   END sp_update_workflow_job;

   FUNCTION fn_get_int_sod_future_adj(
      p_host_id                   IN  VARCHAR2,
      p_entity_id                 IN  VARCHAR2,
      p_currency_code             IN  VARCHAR2,
      p_account_id                IN  VARCHAR2,
      p_account_type              IN  VARCHAR2,
      p_bv_adjust_basis_forecast      VARCHAR2,
      p_future_balance_method         VARCHAR2,
      p_value_date                IN  DATE,
      p_current_date              IN  DATE
   )
   RETURN NUMBER
   IS
      v_nearest_balance_date   DATE;
      v_start_balance          NUMBER(22,4) := 0;
      v_curr_dt                DATE := p_current_date;
   BEGIN
      -- Are we dealing with a future date?
      IF p_value_date > p_current_date THEN
         IF p_future_balance_method = 'Z' THEN
             RETURN 0;
         ELSE
            -- Determine values to pass to fn_get_start_balance
            IF p_future_balance_method = 'N' THEN
               -- Must determine 'nearest' balance date available
               -- between today and p_value_date

               v_nearest_balance_date := fnGetNearestBalDate(
                  p_host_id,
                  p_entity_id,
                  p_account_id,
                  p_account_type,
                  p_value_date,
                  p_current_date
               );

               -- If no nearest record found then call with current date = p_current_date
               -- thus keeping existing functionality
               IF v_nearest_balance_date IS NOT NULL THEN
                  v_curr_dt := v_nearest_balance_date;
               END IF;
            END IF;
         END IF;
      END IF;

      v_start_balance := fn_get_start_balance(
         p_host_id,
         p_entity_id,
         p_currency_code,
         p_account_id,
         p_account_type,
         p_bv_adjust_basis_forecast,
         p_value_date,
         v_curr_dt
      );

      RETURN NVL(v_start_balance,0);
   END fn_get_int_sod_future_adj;

   FUNCTION fn_get_ext_sod_future_adj(
      p_host_id                   IN  VARCHAR2,
      p_entity_id                 IN  VARCHAR2,
      p_currency_code             IN  VARCHAR2,
      p_account_id                IN  VARCHAR2,
      p_account_type              IN  VARCHAR2,
      p_bv_adjust_basis_external  IN  VARCHAR2,
      p_future_balance_method         VARCHAR2,
      p_value_date                IN  DATE,
      p_current_date              IN  DATE
   )
   RETURN NUMBER IS
      v_nearest_balance_date   DATE;
      v_start_balance          NUMBER(22,4) := 0;
      v_curr_dt                DATE := p_current_date;
   BEGIN
      -- Are we dealing with a future date?
      IF p_value_date > p_current_date THEN
         IF p_future_balance_method = 'Z' THEN
            RETURN 0;
         ELSE
            -- Determine values to pass to FN_GET_START_BAL_EXTERNAL
            IF p_future_balance_method = 'N' THEN
               -- Must determine 'nearest' balance date available
               -- between today and p_value_date
               v_nearest_balance_date := fnGetNearestBalDate(
                  p_host_id,
                  p_entity_id,
                  p_account_id,
                  p_account_type,
                  p_value_date,
                  p_current_date
               );

               -- If no nearest record found then call with current date = p_current_date
               -- thus keeping existing functionality
               IF v_nearest_balance_date IS NOT NULL THEN
                  v_curr_dt := v_nearest_balance_date;
               END IF;
            END IF;
         END IF;
      END IF;

      v_start_balance := fn_get_start_bal_external(
         p_host_id,
         p_entity_id,
         p_currency_code,
         p_account_id,
         p_account_type,
         p_bv_adjust_basis_external,
         p_value_date,
         v_curr_dt
      );

      RETURN NVL(v_start_balance,0);
   END fn_get_ext_sod_future_adj;

   FUNCTION fn_get_loro_sod_future_adj(
      p_host_id                  IN  VARCHAR2,
      p_entity_id                IN  VARCHAR2,
      p_currency_code            IN  VARCHAR2,
      p_account_id               IN  VARCHAR2,
      p_account_type             IN  VARCHAR2,
      p_account_class            IN  VARCHAR2,
      p_this_entity_incl_bal_flag  IN  VARCHAR2,
      p_bv_adjust_basis_forecast IN  VARCHAR2,
      p_future_balance_method        VARCHAR2,
      p_value_date               IN  DATE,
      p_current_date             IN  DATE
   )
   RETURN NUMBER IS
      v_nearest_balance_date   DATE;
      v_start_balance          NUMBER(22,4) := 0;
      v_curr_dt                DATE := p_current_date;
   BEGIN
      -- Are we dealing with a future date?
      IF p_value_date > p_current_date THEN
         IF p_future_balance_method = 'Z' THEN
            RETURN 0;
         ELSE
            -- Determine values to pass to FN_GET_START_BAL_LORO
            IF p_future_balance_method = 'N' THEN
               -- Must determine 'nearest' balance date available
               -- between today and p_value_date
               v_nearest_balance_date := fnGetNearestBalDate(
                  p_host_id,
                  p_entity_id,
                  p_account_id,
                  p_account_type,
                  p_value_date,
                  p_current_date
               );

              -- If no nearest record found then call with current date = p_current_date
              -- thus keeping existing functionality
               IF v_nearest_balance_date IS NOT NULL THEN
                  v_curr_dt := v_nearest_balance_date;
               END IF;
            END IF;
         END IF;
      END IF;

      v_start_balance := fn_get_start_bal_loro(
         p_host_id,
         p_entity_id,
         p_currency_code,
         p_account_id,
         p_account_type,
         p_account_class,
         p_this_entity_incl_bal_flag,
         p_bv_adjust_basis_forecast,
         p_value_date,
         v_curr_dt
      );

      RETURN NVL(v_start_balance,0);
   END fn_get_loro_sod_future_adj;

   FUNCTION fn_get_sod_balance(
      p_host_id          IN  VARCHAR2,
      p_entity_id        IN  VARCHAR2,
      p_account_id       IN  VARCHAR2,
      p_account_type     IN  VARCHAR2,
      p_bv_adjust_basis      VARCHAR2,
      p_value_date       IN  DATE,
      p_current_date     IN  DATE,
      p_basis_type           VARCHAR2
   )
   RETURN NUMBER IS
      vSODBal  NUMBER(22,4) :=0;
      vDate    DATE;
   BEGIN
      IF p_value_date <= p_current_date THEN
        vDate :=   p_value_date;
      ELSE
        vDate :=   p_current_date;
      END IF;

      BEGIN
          SELECT
                CASE
                    WHEN p_basis_type = 'I'
                    THEN
                       DECODE(NVL(p_bv_adjust_basis,'N'), 'N', NVL(b.working_forecast_sod,0),
                                                               (  NVL(b.working_forecast_sod,0)
                                                                + NVL(b.bv_forecast_adjust,0)
                                                               )
                             )
                    WHEN p_basis_type = 'E'
                    THEN
                       DECODE(NVL(p_bv_adjust_basis,'N'), 'N', NVL(b.working_external_sod,0),
                                                               (  NVL(b.working_external_sod,0)
                                                                + NVL(b.bv_external_adjust,0)
                                                               )
                             )
                END
           INTO vSODBal
           FROM p_balance b
          WHERE b.host_id      = p_host_id
            AND b.entity_id    = p_entity_id
            AND b.balance_date = vDate
            AND b.bal_type_id  = p_account_id
            AND b.balance_type = p_account_type;
      EXCEPTION
         WHEN NO_DATA_FOUND THEN
            vSODBal := 0;
      END;

      RETURN vSODBal;
   END fn_get_sod_balance;

   FUNCTION fn_get_start_balance(
      p_host_id                  IN  VARCHAR2,
      p_entity_id                IN  VARCHAR2,
      p_currency_code            IN  VARCHAR2,
      p_account_id               IN  VARCHAR2,
      p_account_type             IN  VARCHAR2,
      p_bv_adjust_basis_forecast IN  VARCHAR2,
      p_value_date               IN  DATE,
      p_current_date             IN  DATE
   )
   RETURN NUMBER IS
      v_start_balance   NUMBER(22,4) := 0;
      v_start_bal       NUMBER(22,4) := 0;
      v_pred_bal        NUMBER(22,4) := 0;
   BEGIN
      v_start_bal := fn_get_sod_balance(
         p_host_id,
         p_entity_id,
         p_account_id,
         p_account_type,
         p_bv_adjust_basis_forecast,
         p_value_date,
         p_current_date,
         'I'
      );

       IF p_value_date > p_current_date THEN
          SELECT SUM(amt)
            INTO v_pred_bal
            FROM (SELECT
                        CASE m.sign
                           WHEN 'C'
                           THEN
                              m.amount
                           WHEN 'D'
                           THEN
                              -m.amount
                        END amt
                   FROM p_movement m
                  WHERE m.host_id = p_host_id
                    AND m.entity_id = p_entity_id
                    AND m.value_date >= p_current_date
                    AND m.value_date <= p_value_date - 1
                    AND m.account_id = p_account_id
                    AND m.currency_code = p_currency_code
                    AND m.predict_status = 'I'
                    AND m.match_status != 'A'
                    AND m.match_status != 'R'
                );
       END IF;
       v_start_balance := NVL(v_start_bal,0) + NVL(v_pred_bal,0);
       RETURN NVL(v_start_balance,0);
   END fn_get_start_balance;

   FUNCTION fn_get_start_bal_external(
      p_host_id                   IN  VARCHAR2,
      p_entity_id                 IN  VARCHAR2,
      p_currency_code             IN  VARCHAR2,
      p_account_id                IN  VARCHAR2,
      p_account_type              IN  VARCHAR2,
      p_bv_adjust_basis_external  IN  VARCHAR2,
      p_value_date                IN  DATE,
      p_current_date              IN  DATE
   )
   RETURN NUMBER IS
      v_start_balance  NUMBER(22,4) := 0;
      v_start_bal      NUMBER(22,4) := 0;
      v_pred_bal       NUMBER(22,4) := 0;
   BEGIN
      v_start_bal := fn_get_sod_balance (
         p_host_id,
         p_entity_id,
         p_account_id,
         p_account_type,
         p_bv_adjust_basis_external,
         p_value_date,
         p_current_date,
         'E'
      );

      IF p_value_date > p_current_date THEN
         SELECT SUM(amt)
           INTO v_pred_bal
           FROM (SELECT
                        CASE m.sign
                          WHEN 'C'
                          THEN
                             m.amount
                          WHEN 'D'
                          THEN
                             -m.amount
                        END amt
                   FROM p_movement m
                  WHERE m.host_id = p_host_id
                    AND m.entity_id = p_entity_id
                    AND m.currency_code = p_currency_code
                    AND m.value_Date >= p_current_date
                    AND m.value_Date <= p_value_date - 1
                    AND m.ext_bal_status = 'I'
                    AND (
                          m.predict_status = 'I' OR
                          m.predict_status = 'E'
                        )
                    AND m.match_Status != 'A'
                    AND m.match_Status != 'R'
                    AND m.account_id = p_account_id
                );
      END IF;

       v_start_balance := NVL(v_start_bal,0) + NVL(v_pred_bal,0);
       RETURN NVL(v_start_balance,0);
   END fn_get_start_bal_external;

   FUNCTION fn_get_start_bal_loro(
      p_host_id                    IN  VARCHAR2,
      p_entity_id                  IN  VARCHAR2,
      p_currency_code              IN  VARCHAR2,
      p_account_id                 IN  VARCHAR2,
      p_account_type               IN  VARCHAR2,
      p_account_class              IN  VARCHAR2,
      p_this_entity_incl_bal_flag  IN  VARCHAR2,
      p_bv_adjust_basis_forecast   IN  VARCHAR2,
      p_value_date                 IN  DATE,
      p_current_date               IN  DATE
   )
   RETURN NUMBER IS
      vStartBalance  NUMBER(22,4) := 0;
      vStartBal      NUMBER(22,4) := 0;
      vPredBal       NUMBER(22,4) := 0;
   BEGIN

      IF p_account_class IN ('L' , 'C')
      THEN
         vStartBal := fn_get_sod_balance(
            p_host_id,
            p_entity_id,
            p_account_id,
            p_account_type,
            p_bv_adjust_basis_forecast,
            p_value_date,
            p_current_date,
            'I'
         );

         IF p_value_date > p_current_date
         THEN
            SELECT SUM(amt)
              INTO vPredBal
              FROM (SELECT
                           CASE m.sign
                              WHEN 'C'
                              THEN
                                 m.amount
                              WHEN 'D'
                              THEN
                                 -m.amount
                           END amt
                      FROM p_movement m,
                           p_position_level_name p
                     WHERE m.host_id = p_host_id
                       AND m.entity_id = p_entity_id
                       AND m.value_date >= p_current_date
                       AND m.value_date <= p_value_date - 1
                       AND m.account_id = p_account_id
                       AND m.currency_code = p_currency_code
                       AND m.predict_status = 'I'
                       AND m.match_status NOT IN ('A', 'R')
                       AND p.internal_external = 'I'
                       AND m.host_id = p.host_id
                       AND m.entity_id = p.entity_id
                       AND m.position_level = p.position_level
                   );
          END IF;
      END IF;
      vStartBalance := NVL(vStartBal,0) + NVL(vPredBal,0);
      RETURN NVL(vStartBalance,0);
   END fn_get_start_bal_loro;

   FUNCTION fn_get_predict_balance(
      p_host_id                IN  VARCHAR2,
      p_entity_id              IN  VARCHAR2,
      p_currency_code          IN  VARCHAR2,
      p_account_id             IN  VARCHAR2,
      p_start_balance_option   IN  VARCHAR2,
      p_value_date             IN  DATE,
      p_current_date           IN  DATE
   )
   RETURN NUMBER IS
      vPredictBalance  NUMBER(22,4) := 0;
   BEGIN

       SELECT SUM(amt)
         INTO vPredictBalance
         FROM (SELECT
                      CASE M.SIGN
                          WHEN 'C'
                          THEN
                               m.amount
                          WHEN 'D'
                          THEN
                               -m.amount
                      END amt
                 FROM p_movement m
                WHERE m.host_id = p_host_id
                  AND m.entity_id = p_entity_id
                  AND m.value_date = p_value_date
                  AND m.currency_code = p_currency_code
                  AND m.account_id = p_account_id
                  AND m.predict_status = 'I'
                  AND m.match_status NOT IN ('A', 'R')
              );

       IF p_value_date >= p_current_date
       THEN
          -- Mantis 2276: Remove WITH clause and add hint
          SELECT NVL(SUM(amt), 0) + NVL(vPredictBalance, 0)
            INTO vPredictBalance
            FROM (SELECT /*+ INDEX (M,IDX_MOVE_OPEN_CCY_CODE_ENT_VD) */
                         CASE M.SIGN
                         WHEN 'C' THEN
                              m.amount
                         WHEN 'D' THEN
                              -m.amount
                         END amt
                    -- Use ANSI joins to seperate conditions from joins
                    FROM p_movement m
                         JOIN s_entity e
                            ON (m.host_id = e.host_id AND m.entity_id = e.entity_id)
                   WHERE m.host_id = p_host_id
                     AND m.entity_id = p_entity_id
                     AND m.account_id = p_account_id
                     AND m.currency_code = p_currency_code
                     AND m.predict_status = 'I'
                     AND m.match_status NOT IN ('A', 'R')
                     AND m.OPEN = 'Y'
                     /* has value date earlier than the given one */
                     AND m.value_date < p_current_date
                     AND (
                           -- is not of internal bal pos level, on an internal BV basis account
                           (    p_start_balance_option = 'I'
                            AND m.position_level != e.balance_int_position
                           )
                        -- is not of external bal pos level, on an external BV basis account
                        OR (    p_start_balance_option = 'E'
                            AND m.position_level != e.balance_ext_position
                           )
                        -- is not counted at all due to 'N' setting on account
                        OR p_start_balance_option IN ('P', 'Z')
                       )
                 );
       END IF;
       RETURN NVL(vPredictBalance,0);
   END fn_get_predict_balance;

   FUNCTION fn_get_predict_balance_ext(
      p_host_id         IN  VARCHAR2,
      p_entity_id       IN  VARCHAR2,
      p_currency_code   IN  VARCHAR2,
      p_account_id      IN  VARCHAR2,
      p_value_date      IN  DATE
   )
   RETURN NUMBER IS
      vPredictBalance  NUMBER(22,4) := 0;
   BEGIN
      SELECT SUM(amt)
        INTO vPredictBalance
        FROM (SELECT
                     CASE M.SIGN
                         WHEN 'C'
                         THEN
                            m.amount
                         WHEN 'D'
                         THEN
                            -m.amount
                     END amt
                FROM p_movement m
               WHERE m.host_id = p_host_id
                 AND m.entity_id = p_entity_id
                 AND m.value_date = p_value_date
                 AND m.account_id = p_account_id
                 AND m.currency_code = p_currency_code
                 AND m.ext_bal_status = 'I'
                 AND m.predict_status IN ('I', 'E')
                 AND m.match_status NOT IN ('A', 'R')
             );
      RETURN NVL(vPredictBalance,0);
   END fn_get_predict_balance_ext;

   FUNCTION fn_get_predict_balance_loro(
      p_host_id              IN  VARCHAR2,
      p_entity_id            IN  VARCHAR2,
      p_currency_code        IN  VARCHAR2,
      p_account_id           IN  VARCHAR2,
      p_start_balance_option IN  VARCHAR2,
      p_account_class        IN  VARCHAR2,
      p_this_entity_incl_bal_flag  IN  VARCHAR2,
      p_value_date           IN  DATE,
      p_current_date         IN  DATE
   )
   RETURN NUMBER IS
      vPredictBalance  NUMBER(22,4) := 0;
   BEGIN
      IF p_account_class IN ('L' , 'C')
      THEN
          SELECT SUM(amt)
           INTO vPredictBalance
           FROM ( SELECT
                         CASE m.sign
                           WHEN 'C'
                           THEN
                              m.amount
                           WHEN 'D'
                           THEN
                              -m.amount
                         END amt
                    FROM p_movement m,
                         p_position_level_name p
                   WHERE m.host_id = p_host_id
                     AND m.entity_id = p_entity_id
                     AND m.value_date = p_value_date
                     AND m.account_id = p_account_id
                     AND m.currency_code = p_currency_code
                     AND m.predict_status = 'I'
                     AND m.match_Status NOT IN  ('A', 'R')
                     AND p.internal_external = 'I'
                     AND m.host_id = p.host_id
                     AND m.entity_id = p.entity_id
                     AND m.position_level = p.position_level
                );

          IF     p_value_date = p_current_date
             AND p_start_balance_option = 'I'
          THEN
              -- Mantis 2276: Remove WITH clause and add hint
              SELECT NVL(SUM(amt), 0) + NVL(vPredictBalance, 0)
                INTO vPredictBalance
                FROM (SELECT /*+ INDEX (M,IDX_MOVE_OPEN_CCY_CODE_ENT_VD) */
                              CASE m.sign
                                 WHEN 'C' THEN
                                    m.amount
                                 WHEN 'D' THEN
                                    -m.amount
                              END amt
                         -- Use ANSI joins to seperate conditions from joins
                         FROM p_movement m
                              JOIN p_position_level_name p
                                 ON (m.host_id = p.host_id AND m.entity_id = p.entity_id
                                     AND m.position_level = p.position_level)
                              JOIN s_entity e
                                 ON (m.host_id = e.host_id AND m.entity_id = e.entity_id)
                        WHERE m.host_id = p_host_id
                          AND m.entity_id = p_entity_id
                          AND m.account_id = p_account_id
                          AND m.currency_code = p_currency_code
                          /* Unsettled movements */
                          AND m.predict_status = 'I'
                          AND p.internal_external = 'I'
                          AND m.match_status NOT IN ('A', 'R')
                          AND m.open = 'Y'
                          /* has value date earlier than the given one */
                          AND m.value_date < p_value_date
                          /* is not GL position */
                          AND m.position_level != e.balance_int_position
                     );
          END IF;
      END IF;
      RETURN NVL(vPredictBalance,0);
   END fn_get_predict_balance_loro;

   FUNCTION fn_get_unexpected_balance(
      p_host_id          IN  VARCHAR2,
      p_entity_id        IN  VARCHAR2,
      p_currency_code    IN  VARCHAR2,
      p_account_id       IN  VARCHAR2,
      p_threshold_flag   IN  VARCHAR2 DEFAULT 'Y',
      p_value_date       IN  DATE,
      p_current_date     IN  DATE
   )
   RETURN NUMBER IS
      vUnexpectedBalance  NUMBER(22,4) := 0;
   BEGIN

      IF p_threshold_flag = 'N' THEN
         SELECT SUM(amt)
           INTO vUnexpectedBalance
           FROM (SELECT
                        CASE m.sign
                           WHEN 'C'
                           THEN
                              m.amount
                           WHEN 'D'
                           THEN
                              -m.amount
                        END amt
                   FROM p_movement m,
                        s_entity e,
                        p_position_level_name p
                  WHERE m.host_id = p_host_id
                    AND m.entity_id = p_entity_id
                    AND m.value_date = p_value_date
                    AND m.account_id = p_account_id
                    AND m.currency_code = p_currency_code
                    AND m.predict_status = 'E'
                    AND m.match_status not in ('A', 'R', 'E')
                    AND p.internal_external = 'E'
                    AND m.position_level = e.balance_ext_position
                    AND m.host_id = e.host_id
                    AND m.entity_id = e.entity_id
                    AND e.host_id = p.host_id
                    AND e.entity_id = p.entity_id
                    AND p.position_level = m.position_level
                );
         IF p_value_date = p_current_date THEN
            -- Mantis 2276: Remove WITH clause and add hint
            SELECT NVL(SUM(amt), 0) + NVL(vUnexpectedBalance, 0)
              INTO vUnexpectedBalance
              FROM (SELECT /*+ INDEX (M,IDX_MOVE_OPEN_CCY_CODE_ENT_VD) */
                           CASE m.sign
                              WHEN 'C'
                              THEN
                                 m.amount
                              WHEN 'D'
                              THEN
                                 -m.amount
                           END amt
                      -- Use ANSI joins to seperate conditions from joins
                      -- Remove join with p_position_level_name table because
                      -- s_entity.balance_ext_position must always be an external position
                      FROM p_movement m
                           JOIN s_entity e
                              ON (m.host_id = e.host_id AND m.entity_id = e.entity_id)
                     WHERE m.host_id = p_host_id
                       AND m.entity_id = p_entity_id
                       AND m.account_id = p_account_id
                       AND m.currency_code = p_currency_code
                       AND m.predict_status = 'E'
                       AND m.match_status NOT IN ('A', 'R', 'E')
                       AND m.open = 'Y'
                       /* has value date earlier than the given one */
                       AND m.value_date < p_value_date
                       AND m.position_level = e.balance_ext_position
                   );
         END IF;
      ELSIF p_threshold_flag = 'Y' THEN
         SELECT SUM(amt)
           INTO vUnexpectedBalance
           FROM (SELECT
                        CASE m.sign
                           WHEN 'C'
                           THEN
                              m.amount
                           WHEN 'D'
                           THEN
                              -m.amount
                        END amt
                   FROM p_movement m,
                        s_entity e,
                        p_position_level_name p,
                        s_currency c
                  WHERE c.host_id = p_host_id
                    AND c.entity_id = p_entity_id
                    AND m.account_id = p_account_id
                    AND m.value_Date = p_value_date
                    AND c.currency_code = p_currency_code
                    AND m.amount >= c.threshold_product
                    AND m.predict_status = 'E'
                    AND m.match_Status not in ('A', 'R', 'E')
                    AND p.internal_external = 'E'
                    AND m.host_id = c.host_id
                    AND m.entity_id = c.entity_id
                    AND m.currency_code = c.currency_code
                    AND m.position_level = e.balance_ext_position
                    AND m.host_id = e.host_id
                    AND m.entity_id = e.entity_id
                    AND e.host_id = p.host_id
                    AND e.entity_id = p.entity_id
                    AND p.position_level = m.position_level
                );
         IF p_value_date = p_current_date THEN
            -- Mantis 2276: Remove WITH clause and add hint
            SELECT NVL(SUM(amt), 0) + NVL(vUnexpectedBalance, 0)
              INTO vUnexpectedBalance
              FROM (SELECT /*+ INDEX (M,IDX_MOVE_OPEN_CCY_CODE_ENT_VD) */
                           CASE m.sign
                              WHEN 'C'
                              THEN
                                 m.amount
                              WHEN 'D'
                              THEN
                                 -m.amount
                           END amt
                      -- Use ANSI joins to seperate conditions from joins
                      -- Remove join with p_position_level_name table because
                      -- s_entity.balance_ext_position must always be an external position
                      FROM p_movement m
                           JOIN s_entity e
                              ON (m.host_id = e.host_id AND m.entity_id = e.entity_id)
                           JOIN s_currency c
                              ON (m.host_id = c.host_id AND m.entity_id = c.entity_id
                                  AND m.currency_code = c.currency_code)
                     WHERE m.host_id = p_host_id
                       AND m.entity_id = p_entity_id
                       AND m.account_id = p_account_id
                       AND m.currency_code = p_currency_code
                       AND m.predict_status = 'E'
                       AND m.match_status NOT IN ('A', 'R', 'E')
                       AND m.open = 'Y'
                       /* has value date earlier than the given one */
                       AND m.value_date < p_value_date
                       /* applying currency threshold */
                       AND m.amount >= c.threshold_product
                       AND m.position_level = e.balance_ext_position
                   );
         END IF;
      END IF;
      RETURN NVL(vUnexpectedBalance,0);
   END fn_get_unexpected_balance;

   FUNCTION fn_get_open_unexpected_balance(
      p_host_id        IN  VARCHAR2,
      p_entity_id      IN  VARCHAR2,
      p_currency_code  IN  VARCHAR2,
      p_account_id     IN  VARCHAR2,
      p_account_class  IN  VARCHAR2,
      p_value_date     IN  DATE,
      p_current_date   IN  DATE
   )
   RETURN NUMBER IS
      vOpenUnexpectedBalance  NUMBER(22,4) := 0;
   BEGIN
      IF p_value_date >= p_current_date AND p_account_class IN ('N','O') THEN
         -- Mantis 2276: Remove WITH clause and add hint
         SELECT SUM(amt)
           INTO vOpenUnexpectedBalance
           FROM (SELECT /*+ INDEX (M,IDX_MOVE_OPEN_CCY_CODE_ENT_VD) */
                        CASE M.SIGN
                           WHEN 'C'
                           THEN
                              m.amount
                           WHEN 'D'
                           THEN
                              -m.amount
                        END amt
                   -- Use ANSI joins to seperate conditions from joins
                   -- Remove join with p_position_level_name table because
                   -- s_entity.balance_ext_position must always be an external position
                   FROM p_movement m
                        JOIN s_entity e
                           ON (m.host_id = e.host_id AND m.entity_id = e.entity_id)
                  WHERE m.host_id = p_host_id
                    AND m.entity_id = p_entity_id
                    AND m.account_id = p_account_id
                    AND m.currency_code = p_currency_code
                    AND m.predict_status = 'E'
                    AND m.match_status NOT IN ('A', 'R', 'E')
                    AND m.open = 'Y'
                    /* has value date earlier than the given one */
                    AND m.value_date < p_current_date
                    AND m.position_level = e.balance_ext_position
             );
      END IF;
      RETURN NVL(vOpenUnexpectedBalance,0);
   END fn_get_open_unexpected_balance;

   FUNCTION fn_get_unsettled_balance(
      p_host_id        IN  VARCHAR2,
      p_entity_id      IN  VARCHAR2,
      p_currency_code  IN  VARCHAR2,
      p_account_id     IN  VARCHAR2,
      p_threshold_flag IN  VARCHAR2 DEFAULT 'Y',
      p_value_date     IN  DATE,
      p_current_date   IN  DATE
   )
   RETURN NUMBER IS
      vUnsettledBalance  NUMBER(22,4) := 0;
   BEGIN
      IF p_threshold_flag = 'N' THEN
         SELECT SUM(amt)
           INTO vUnsettledBalance
           FROM (SELECT
                        CASE m.sign
                           WHEN 'C'
                           THEN
                              m.amount
                           WHEN 'D'
                           THEN
                              -m.amount
                        END amt
                   FROM p_movement m,
                        p_position_level_name p
                  WHERE m.host_id = p_host_id
                    AND m.entity_id = p_entity_id
                    AND m.value_date = p_value_date
                    AND m.account_id = p_account_id
                    AND m.currency_code = p_currency_code
                    AND p.internal_external = 'I'
                    AND m.predict_status = 'I'
                    AND m.match_status NOT IN ('A', 'R', 'E')
                    AND m.host_id = p.host_id
                    AND m.entity_id = p.entity_id
                    AND m.position_level = p.position_level
                );
         IF p_value_date = p_current_date THEN
            -- Mantis 2276: Remove WITH clause and add hint
            SELECT NVL(SUM(amt), 0) + NVL(vUnsettledBalance, 0)
              INTO vUnsettledBalance
              FROM (SELECT /*+ INDEX (M,IDX_MOVE_OPEN_CCY_CODE_ENT_VD) */
                           CASE m.sign
                              WHEN 'C'
                              THEN
                                 m.amount
                              WHEN 'D'
                              THEN
                                 -m.amount
                           END amt
                      -- Use ANSI joins to seperate conditions from joins
                      FROM p_movement m
                           JOIN p_position_level_name p
                              ON (m.host_id = p.host_id AND m.entity_id = p.entity_id
                                  AND m.position_level = p.position_level)
                     WHERE m.host_id = p_host_id
                       AND m.entity_id = p_entity_id
                       AND m.account_id = p_account_id
                       AND m.currency_code = p_currency_code
                       /* unsettled movement */
                       AND p.internal_external = 'I'
                       AND m.predict_status = 'I'
                       AND m.match_status NOT IN ('A', 'R', 'E')
                       AND m.OPEN = 'Y'
                       /* has value date earlier than the given one */
                       AND m.value_date < p_value_date
                   );
         END IF;
      ELSIF p_threshold_flag = 'Y' THEN
         SELECT SUM(amt)
           INTO vUnsettledBalance
           FROM (SELECT
                        CASE m.sign
                             WHEN 'C'
                             THEN
                                m.amount
                             WHEN 'D'
                             THEN
                                -m.amount
                        END amt
                   FROM p_movement m,
                        s_currency c,
                        p_position_level_name p
                  WHERE c.host_id = p_host_id
                    AND c.entity_id = p_entity_id
                    AND c.currency_code = p_currency_code
                    AND m.value_date = p_value_date
                    AND m.account_id = p_account_id
                    AND m.amount   >= c.threshold_product
                    AND p.internal_external = 'I'
                    AND m.predict_status = 'I'
                    AND m.match_status NOT IN ('A', 'R', 'E')
                    AND m.host_id = c.host_id
                    AND m.entity_id = c.entity_id
                    AND m.currency_code = c.currency_code
                    AND m.host_id = p.host_id
                    AND m.entity_id = p.entity_id
                    AND m.position_level = p.position_level
                );

         IF p_value_date = p_current_date THEN
            -- Mantis 2276: Remove WITH clause and add hint
            SELECT NVL(SUM(amt),0) + NVL(vUnsettledBalance, 0)
              INTO vUnsettledBalance
              FROM (SELECT /*+ INDEX (M,IDX_MOVE_OPEN_CCY_CODE_ENT_VD) */
                           CASE m.sign
                              WHEN 'C'
                              THEN
                                 m.amount
                              WHEN 'D'
                              THEN
                                 -m.amount
                           END amt
                      -- Use ANSI joins to seperate conditions from joins
                      FROM p_movement m
                           JOIN p_position_level_name p
                              ON (m.host_id = p.host_id AND m.entity_id = p.entity_id
                                  AND m.position_level = p.position_level)
                           JOIN s_currency c
                              ON (m.host_id = c.host_id AND m.entity_id = c.entity_id
                                  AND m.currency_code = c.currency_code)
                     WHERE m.host_id = p_host_id
                       AND m.entity_id = p_entity_id
                       AND m.account_id = p_account_id
                       AND m.currency_code = p_currency_code
                       /* unsettled movement */
                       AND p.internal_external = 'I'
                       AND m.predict_status = 'I'
                       AND m.match_status NOT IN ('A', 'R', 'E')
                       AND m.OPEN = 'Y'
                       /* has value date earlier than the given one */
                       AND m.value_date < p_value_date
                       /* applying currency threshold */
                       AND m.amount >= c.threshold_product
                   );
         END IF;
      END IF;
      RETURN NVL(vUnsettledBalance ,0);
   END fn_get_unsettled_balance;

   FUNCTION fn_exchange_rate(
      p_host_id         IN  VARCHAR2,
      p_entity_id       IN  VARCHAR2,
      p_currency_code   IN  VARCHAR2,
      p_movement_type   IN  VARCHAR2
   )
   RETURN NUMBER IS
   BEGIN
      IF p_movement_type = 'C' THEN
         RETURN ex_mat(p_host_id)(p_entity_id)(p_currency_code).cash_filter;
      ELSE
         RETURN ex_mat(p_host_id)(p_entity_id)(p_currency_code).securities_filter;
      END IF;
   END fn_exchange_rate;

   FUNCTION fnGetNearestBalDate (
      p_host_id      IN  p_account.host_id%TYPE,
      p_entity_id    IN  p_account.entity_id%TYPE,
      p_account_id   IN  p_account.account_id%TYPE,
      p_account_type IN  p_account.account_type%TYPE,
      p_value_date   IN  DATE,
      p_current_date IN  DATE
   )
   RETURN DATE IS
     vBalDate        DATE;
   BEGIN
     SELECT MAX(balance_date)
       INTO vBalDate
       FROM p_balance
      WHERE balance_date BETWEEN p_current_date AND p_value_date
        AND host_id      = p_host_id
        AND entity_id    = p_entity_id
        AND balance_type = p_account_type
        AND bal_type_id  = p_account_id;

     RETURN vBalDate;
   END fnGetNearestBalDate;


   -- ----------------------------------------------------------------------------------------------
   -- Returns the number of days before today
   -- ----------------------------------------------------------------------------------------------
   FUNCTION Get_Days_Before_Today
   RETURN PLS_INTEGER
   IS
      DAYS_BEFORE_TODAY_DEFAULT   CONSTANT PLS_INTEGER   := 3;
      DAYS_BEFORE_TODAY_MIN       CONSTANT PLS_INTEGER   := 1;
      DAYS_BEFORE_TODAY_MAX       CONSTANT PLS_INTEGER   := 100;
      n_days                      PLS_INTEGER;
   BEGIN
      BEGIN
         SELECT TO_NUMBER(PK_APPLICATION.FN_GET_PARAMETER_VALUE(
                   '*DEFAULT*',
                   'PREDICT_JOBS',
                   'DAYS_BEFORE_TODAY',
                   DAYS_BEFORE_TODAY_DEFAULT
                ))
                INTO n_days
           FROM DUAL;

         -- Check the value is valid and between min and max range
         IF (n_days IS NULL) THEN
            n_days := DAYS_BEFORE_TODAY_DEFAULT;
         ELSIF n_days < DAYS_BEFORE_TODAY_MIN THEN
            n_days := DAYS_BEFORE_TODAY_MIN;
         ELSIF n_days > DAYS_BEFORE_TODAY_MAX THEN
            n_days := DAYS_BEFORE_TODAY_MAX;
         END IF;
      EXCEPTION
         WHEN OTHERS THEN
            n_days := DAYS_BEFORE_TODAY_DEFAULT;
      END;

      RETURN TRUNC(n_days);
   END;


   -- ----------------------------------------------------------------------------------------------
   -- Returns the number of days after today
   -- ----------------------------------------------------------------------------------------------
   FUNCTION Get_Days_After_Today
   RETURN PLS_INTEGER
   IS
      DAYS_AFTER_TODAY_DEFAULT   CONSTANT PLS_INTEGER   := 6;
      DAYS_AFTER_TODAY_MIN       CONSTANT PLS_INTEGER   := 1;
      DAYS_AFTER_TODAY_MAX       CONSTANT PLS_INTEGER   := 100;
      n_days                     PLS_INTEGER;
   BEGIN
      BEGIN
         SELECT TO_NUMBER(PK_APPLICATION.FN_GET_PARAMETER_VALUE(
                   '*DEFAULT*',
                   'PREDICT_JOBS',
                   'DAYS_AFTER_TODAY',
                   DAYS_AFTER_TODAY_DEFAULT
                ))
                INTO n_days
           FROM DUAL;

         -- Check the value is valid and between min and max range
         IF (n_days IS NULL) THEN
            n_days := DAYS_AFTER_TODAY_DEFAULT;
         ELSIF n_days < DAYS_AFTER_TODAY_MIN THEN
            n_days := DAYS_AFTER_TODAY_MIN;
         ELSIF n_days > DAYS_AFTER_TODAY_MAX THEN
            n_days := DAYS_AFTER_TODAY_MAX;
         END IF;
      EXCEPTION
         WHEN OTHERS THEN
            n_days := DAYS_AFTER_TODAY_DEFAULT;
      END;

      RETURN TRUNC(n_days);
   END;


   -- ----------------------------------------------------------------------------------------------
   -- Returns the number of days after today (Books)
   -- ----------------------------------------------------------------------------------------------
   FUNCTION Get_Days_After_Today_Books
   RETURN PLS_INTEGER
   IS
      DAYS_AFTER_TODAY_BOOK_DEFAULT   CONSTANT PLS_INTEGER   := 29;
      DAYS_AFTER_TODAY_BOOK_MIN       CONSTANT PLS_INTEGER   := 1;
      DAYS_AFTER_TODAY_BOOK_MAX       CONSTANT PLS_INTEGER   := 100;
      n_days                          PLS_INTEGER;
   BEGIN
      BEGIN
         SELECT TO_NUMBER(PK_APPLICATION.FN_GET_PARAMETER_VALUE(
                   '*DEFAULT*',
                   'PREDICT_JOBS',
                   'DAYS_AFTER_TODAY_BOOK',
                   DAYS_AFTER_TODAY_BOOK_DEFAULT
                ))
                INTO n_days
           FROM DUAL;

         -- Check the value is valid and between min and max range
         IF (n_days IS NULL) THEN
            n_days := DAYS_AFTER_TODAY_BOOK_DEFAULT;
         ELSIF n_days < DAYS_AFTER_TODAY_BOOK_MIN THEN
            n_days := DAYS_AFTER_TODAY_BOOK_MIN;
         ELSIF n_days > DAYS_AFTER_TODAY_BOOK_MAX THEN
            n_days := DAYS_AFTER_TODAY_BOOK_MAX;
         END IF;
      EXCEPTION
         WHEN OTHERS THEN
            n_days := DAYS_AFTER_TODAY_BOOK_DEFAULT;
      END;

      RETURN TRUNC(n_days);
   END;


   -- ----------------------------------------------------------------------------------------------
   -- Calculates the number of excluded movements for dates after system dates to fill in
   -- the p_workflow_chart_temp table
   -- ----------------------------------------------------------------------------------------------
   PROCEDURE SP_UPDATE_WORKFLOW_NEXT_DAYS
   IS
       v_system_date      DATE;
   BEGIN
       -- call PREPARE_WORKFLOW_CHART_TEMP to delete rows out of range
       PKG_WORKFLOW_LIB.PREPARE_WORKFLOW_CHART_TEMP;

       -- Start process for each entity
       FOR v_EntRecTyp IN pkg_entity_process.ent_cv(vMonitorPopulateData)
       LOOP
            -- Execute the update job only if conditions for populate job, running for the same entity are NOT satisfied
           IF pkg_entity_process.FnIsProcessToRun(v_EntRecTyp.entity_id,
                                                  v_EntRecTyp.run_time,
                                                  v_EntRecTyp.process_name,
                                                  'SECONDARY')
           THEN
               v_system_date:= TRUNC(GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(GLOBAL_VAR.SYS_DATE,v_EntRecTyp.entity_id));
               DECLARE
                   CURSOR cr_internal_movements (seq_days in number) IS
                         SELECT  COUNT (wot_mvmt_id) wot_count,
                                 COUNT (wt_mvmt_id) wt_count,
                                 host_id,
                                 entity_id,
                                 currency_group_id,
                                 position_level
                            FROM (SELECT m.movement_id wot_mvmt_id,
                                         CASE
                                            WHEN m.amount >= s.threshold_product THEN m.movement_id
                                            ELSE NULL
                                         END
                                            wt_mvmt_id,
                                         m.position_level,
                                         m.host_id,
                                         m.entity_id,
                                         s.currency_group_id
                                    FROM p_movement m, s_currency s
                                   WHERE     m.host_id = vHostID
                                         AND m.entity_id = v_EntRecTyp.entity_id
                                         AND m.predict_status = 'I'
                                         AND m.match_status != 'E'
                                         AND m.value_date = v_system_date + seq_days
                                         AND s.host_id = m.host_id
                                         AND s.entity_id = m.entity_id
                                         AND s.currency_code = m.currency_code)
                        GROUP BY host_id,
                                 entity_id,
                                 currency_group_id,
                                 position_level;
               BEGIN
                   UPDATE p_workflow_chart_temp
                      SET t_included_movements_count = 0,
                          included_movements_count = 0
                    WHERE host_id = vHostID
                      AND entity_id = v_EntRecTyp.entity_id
                      AND VALUE_DATE > v_system_date;

                   FOR I IN 1..CONST_WORKFLOW_DAYS LOOP
                       FOR r_data IN cr_internal_movements(I)
                       LOOP
                           UPDATE p_workflow_chart_temp
                              SET included_movements_count = r_data.wot_count,
                                  t_included_movements_count = r_data.wt_count
                            WHERE host_id = r_data.host_id
                              AND entity_id = r_data.entity_id
                              AND currency_group_id = r_data.currency_group_id
                              AND position_level = r_data.position_level
                              AND VALUE_DATE = v_system_date + I;
                       END LOOP;
                   END LOOP;

                   COMMIT;
               END;

               DECLARE
                   CURSOR cr_excluded_movements(seq_days in number, p_currency_code in s_currency.currency_code%type)
                       IS SELECT COUNT (wot_mvmt_id) wot_count,
                                 COUNT (wt_mvmt_id) wt_count,
                                 host_id,
                                 entity_id,
                                 currency_group_id,
                                 position_level
                            FROM (SELECT a.movement_id wot_mvmt_id,
                                         CASE
                                         WHEN a.amount >= s.threshold_product THEN
                                             a.movement_id
                                         ELSE
                                             NULL
                                         END wt_mvmt_id,
                                         s.host_id,
                                         s.entity_id,
                                         s.currency_group_id,
                                         a.position_level
                                    FROM p_movement a, s_currency s
                                   WHERE a.match_status = 'L'
                                     AND a.predict_status = 'E'
                                     AND a.value_date = v_system_date + seq_days
                                     AND s.entity_id = v_EntRecTyp.entity_id
                                     AND 'L' <> (SELECT account_class
                                                   FROM p_account c
                                                  WHERE c.host_id = a.host_id
                                                    AND c.entity_id = a.entity_id
                                                    AND c.entity_id = v_EntRecTyp.entity_id
                                                    AND c.account_id = a.account_id)
                                     AND a.amount >= fn_exchange_rate(
                                                       s.host_id,
                                                       s.entity_id,
                                                       s.currency_code,
                                                       a.movement_type
                                                      )
                                    AND s.host_id = a.host_id
                                    AND s.entity_id = a.entity_id
                                    AND s.currency_code = a.currency_code
                                    AND s.currency_code = p_currency_code
                                  UNION ALL
                                 SELECT a.movement_id wot_mvmt_id,
                                        CASE
                                        WHEN a.amount >= s.threshold_product THEN
                                            a.movement_id
                                        ELSE
                                            NULL
                                        END wt_mvmt_id,
                                        s.host_id,
                                        s.entity_id,
                                        s.currency_group_id,
                                        a.position_level
                                   FROM p_movement a, s_currency s
                                  WHERE a.match_status = 'L'
                                    AND a.predict_status = 'E'
                                    AND a.open = 'Y'
                                    AND a.value_date < v_system_date + seq_days
                                    AND s.entity_id = v_EntRecTyp.entity_id
                                    AND 'L' <> (SELECT account_class
                                                  FROM p_account c
                                                 WHERE c.host_id = a.host_id
                                                   AND c.entity_id = a.entity_id
                                                   AND c.entity_id = v_EntRecTyp.entity_id
                                                   AND c.account_id = a.account_id)
                                    AND a.amount >= fn_exchange_rate(
                                                      s.host_id,
                                                      s.entity_id,
                                                      s.currency_code,
                                                      a.movement_type
                                                    )
                                    AND s.host_id = a.host_id
                                    AND s.entity_id = a.entity_id
                                    AND s.currency_code = a.currency_code
                                    AND s.currency_code = p_currency_code
                                 )
                           GROUP BY host_id, entity_id, currency_group_id, position_level;

                   CURSOR cr_exchange_rate
                       IS SELECT s.host_id,
                                 s.entity_id,
                                 s.currency_code,
                            CASE e.exchange_rate_format
                                 WHEN '1'
                                 THEN (  e.cash_filter
                                        * pk_application.fn_get_currency_exchange_rate (s.host_id,
                                                                                        s.entity_id,
                                                                                        s.currency_code
                                                                                       )
                                      )
                                 ELSE (  e.cash_filter
                                       / pk_application.fn_get_currency_exchange_rate (s.host_id,
                                                                                       s.entity_id,
                                                                                       s.currency_code
                                                                                      )
                                      )
                            END cash_filter,
                            CASE e.exchange_rate_format
                                 WHEN '1'
                                 THEN (  e.securities_filter
                                       * pk_application.fn_get_currency_exchange_rate (s.host_id,
                                                                                       s.entity_id,
                                                                                       s.currency_code
                                                                                      )
                                      )
                                 ELSE (  e.securities_filter
                                       / pk_application.fn_get_currency_exchange_rate (s.host_id,
                                                                                       s.entity_id,
                                                                                       s.currency_code
                                                                                      )
                                      )
                            END securities_filter
                       FROM s_currency s, s_entity e
                      WHERE e.host_id = s.host_id
                        AND e.entity_id = s.entity_id
                        AND e.entity_id = v_EntRecTyp.entity_id;
               BEGIN
                  FOR r_data IN cr_exchange_rate
                  LOOP
                     ex_mat (r_data.host_id) (r_data.entity_id) (r_data.currency_code).cash_filter :=
                        r_data.cash_filter;
                     ex_mat (r_data.host_id) (r_data.entity_id) (r_data.currency_code).securities_filter :=
                        r_data.securities_filter;
                  END LOOP;

                  UPDATE p_workflow_chart_temp
                     SET t_excluded_movements_count = 0,
                         excluded_movements_count = 0
                   WHERE host_id = vHostID
                     AND entity_id = v_EntRecTyp.entity_id
                     AND VALUE_DATE > v_system_date;

                  FOR I IN 1..CONST_WORKFLOW_DAYS LOOP
                    FOR R in cr_exchange_rate loop
                       FOR r_data IN cr_excluded_movements(I, r.currency_code)
                       LOOP
                          UPDATE p_workflow_chart_temp
                             SET excluded_movements_count = excluded_movements_count + r_data.wot_count,
                                 t_excluded_movements_count = t_excluded_movements_count + r_data.wt_count
                           WHERE host_id = r_data.host_id
                             AND entity_id = r_data.entity_id
                             AND currency_group_id = r_data.currency_group_id
                             AND position_level = r_data.position_level
                             AND VALUE_DATE = v_system_date + I;
                       END LOOP;
                    END loop;
                  END LOOP;
                  COMMIT;
               END;
           END IF;
       END LOOP;
   EXCEPTION
      WHEN OTHERS
      THEN
         sp_error_log(
            vHostID,
            CONST_SYSTEM,
            CONST_DBSERVER,
            'PKG_PREDICT_JOBS.SP_UPDATE_WORKFLOW_NEXT_DAYS',
            SQLCODE,
            SQLERRM
         );
   END SP_UPDATE_WORKFLOW_NEXT_DAYS;

   FUNCTION fn_get_preadvice_amounts (P_HOST_ID       P_ACCOUNT.HOST_ID%TYPE,
                                      P_ENTITY_ID     P_ACCOUNT.ENTITY_ID%TYPE,
                                      P_ACCOUNT_ID    P_ACCOUNT.ACCOUNT_ID%TYPE,
                                      P_VALUE_DATE    DATE) RETURN NUMBER
   IS
        VN_PREADVICES       P_MOVEMENT.AMOUNT%TYPE;
   BEGIN
        SELECT SUM(P.AMOUNT)
          INTO VN_PREADVICES
          FROM P_MOVEMENT P
               INNER JOIN S_ENTITY E ON (P.HOST_ID = E.HOST_ID AND P.ENTITY_ID = E.ENTITY_ID)
         WHERE P.HOST_ID = P_HOST_ID
           AND P.ENTITY_ID = P_ENTITY_ID
           AND P.ACCOUNT_ID = P_ACCOUNT_ID
           AND P.VALUE_DATE = P_VALUE_DATE
           AND P.POSITION_LEVEL = E.PREADVICE_POSITION
           AND P.PREDICT_STATUS = 'I' ;

         RETURN NVL(VN_PREADVICES, 0);
   END;

END PKG_PREDICT_JOBS;
/